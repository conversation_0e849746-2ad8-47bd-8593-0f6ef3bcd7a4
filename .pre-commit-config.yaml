
# default is pre-commit, add commit-msg to support conventional commit messages
default_install_hook_types:
  - pre-commit
  - commit-msg

repos:
  - repo: https://github.com/crate-ci/typos
    rev: v1.16.18
    hooks:
      - id: typos
        # args: [--write-changes, --force-exclude]
        args: [--force-exclude]
        exclude: |
          (?x)^(
            .*get-pip.py.*|
            .*testcases.*yaml
          )$


  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        stages: [commit]
      - id: end-of-file-fixer
        stages: [commit]
      - id: check-yaml
        stages: [commit]
        exclude: |
          (?x)^(
            .*testcases.*yaml
          )$
      - id: check-json
        exclude: ^(tsconfig\.json|.vscode/launch\.json|.vscode/tasks\.json)$
        stages: [commit]
      - id: check-toml
        stages: [commit]
      - id: check-merge-conflict
        stages: [commit]
      - id: check-case-conflict
        stages: [commit]
      - id: detect-private-key
        stages: [commit]


  - repo: https://github.com/compilerla/conventional-pre-commit
    rev: v3.1.0
    hooks:
      - id: conventional-pre-commit
        stages: [commit-msg]
        args: [chore, ci, docs, feat, fix, refactor, perf, style, test] # optional: list of Conventional Commits types to allow e.g. [feat, fix, ci, chore, test]
