{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 6, "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/naming-convention": "warn", "@typescript-eslint/semi": "warn", "curly": "warn", "eqeqeq": "warn", "no-throw-literal": "warn", "quotes": ["error", "single", {"avoidEscape": true}], "comma-dangle": ["warn", "only-multiline"], "semi": "warn"}, "ignorePatterns": ["out", "dist", "**/*.d.ts"]}