import * as vscode from 'vscode';
import { Disposable } from 'vscode';
import { onConfigChanged, getCopilotConf } from '@/config';
import { appChannel } from '@/extension';
import { CHANNEL_DEV } from '@/config';

export let nextEditIsEditable = false;

/**
 * 初始化 VS Code 上下文管理
 * 设置各种 context keys 用于控制命令和菜单的可见性
 */
export function initContextManager(): Disposable[] {
  // 初始化时设置所有 context
  updateAllContexts();

  // 监听配置变化
  const configDisposable = onConfigChanged(() => {
    updateAllContexts();
  });

  return [configDisposable];
}

/**
 * 更新所有 context keys
 */
export function updateAllContexts() {
  updateInlineChatContext();
  updateBlockEditContext();
}

/**
 * 更新内联聊天相关的 context
 */
export function updateInlineChatContext() {
  const config = getCopilotConf();
  const inlineChatEnabled = config.get<boolean>('inlineChat.enabled', false);

  vscode.commands.executeCommand(
    'setContext',
    'li.codebuddy.copilot.inlineChat.enabled',
    !!inlineChatEnabled
  );
}

/**
 * 更新块编辑相关的 context
 */
export function updateBlockEditContext(acceptable = false) {
  const config = getCopilotConf();
  const blockEditEnabled = config.get<boolean>('nextEdit.enabled', false);

  vscode.commands.executeCommand(
    'setContext',
    'li.codebuddy.copilot.nextEdit.enabled',
    !!blockEditEnabled || appChannel() === CHANNEL_DEV
  );
  vscode.commands.executeCommand(
    'setContext',
    'li.codebuddy.copilot.nextEdit.acceptable',
    acceptable
  );
  nextEditIsEditable = acceptable;
}
