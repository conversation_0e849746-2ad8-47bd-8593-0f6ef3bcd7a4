import * as vscode from 'vscode';
import { persistentEvent } from '@/telemetry';
import { localStore, userInstalledExtensionLastReportTimeKey } from '@/store';

export class UserInstalledExtensionsTracker {
  static #instance: UserInstalledExtensionsTracker;
  private periodHours = 24;

  public static get instance() {
    return (this.#instance ??= new this());
  }

  public scheduleReport() {
    setTimeout(() => { this.checkAndReport(); }, 60 * 1000);
    setInterval(() => { this.checkAndReport(); }, 2 * 60 * 60 * 1000);
  }

  private shouldReport(lastTimestamp: number | undefined, periodHours: number): boolean {
    if (!lastTimestamp) {
      return true;
    }
    return Date.now() - lastTimestamp > periodHours * 60 * 60 * 1000;
  }

  private async checkAndReport() {
    await localStore.load();
    const last = localStore.data[userInstalledExtensionLastReportTimeKey];
    if (this.shouldReport(last, this.periodHours)) {
      this.report();
    }
  }

  private async report() {
    const extensions = this.getInstalledExtensions();
    const details = {
      extensions: extensions,
      timestamp: new Date().toISOString(),
    };
    const event = persistentEvent({
      name: 'copilot:user-installed-extensions-list',
    });
    event.end({
      details: details,
    });
    await localStore.load();
    localStore.data[userInstalledExtensionLastReportTimeKey] = Date.now();
    await localStore.save();
  }

  private getInstalledExtensions(): any[] {
    // 获取所有扩展
    const allExtensions = vscode.extensions.all;
    // 过滤出非内置扩展 (用户安装的)
    const userInstalledExtensions = allExtensions.filter(extension =>
      !extension.packageJSON.isBuiltin // 检查 isBuiltin 标志
    );
    return userInstalledExtensions.map(extension => {
      return {
        id: extension.id,
        extensionKind: extension.extensionKind,
        version: extension.packageJSON.version,
        publisher: extension.packageJSON.publisher,
        name: extension.packageJSON.name,
        displayName: extension.packageJSON.displayName,
        description: extension.packageJSON.description,
        categories: extension.packageJSON.categories,
        keywords: extension.packageJSON.keywords,
        engines: extension.packageJSON.engines,
        homepage: extension.packageJSON.homepage,
      };
    });
  }
}
