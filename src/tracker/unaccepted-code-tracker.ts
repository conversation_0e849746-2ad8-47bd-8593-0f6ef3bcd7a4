import * as vscode from 'vscode';
import { randomUUID } from 'crypto';
import { LRU, sleep } from '@/utils/index';
import { CodeSnippet, reportCodeSnippet } from '@/api/tracker';

const PREFIX_LINE_CNT = 30;
const SUFFIX_LINE_CNT = 30;

// 上报间隔
const reportIntervalMinutes = [1, 3, 5];

// 按照一秒一次补全来算都足够存50分钟的accepted id
const acceptedCompletionIDs = new LRU<string, string>(3000);

const getAcceptedAt = (key: string | undefined) => {
  if (key) {
    return acceptedCompletionIDs.get(key);
  }
  return undefined;
};

interface ReportItem extends CodeSnippet {
  doc?: vscode.TextDocument;
  position?: vscode.Position;
  completionID?: string;
  acceptedAt?: string;
}

export const acceptCompletionID = (completionID: string) => {
  if (!completionID) {
    console.log('bug: should have completionID but got ', completionID);
    return;
  }
  acceptedCompletionIDs.add(completionID, new Date().toISOString());
};

const getPrefixAndSuffixAtPosition = (
  doc?: vscode.TextDocument,
  position?: vscode.Position
) => {
  if (!doc || !position) {
    return { prefix: '', suffix: '' };
  }
  const line = position.line;

  const startPos = new vscode.Position(
    Math.max(0, line - PREFIX_LINE_CNT),
    0
  );
  const endPos = new vscode.Position(
    Math.min(doc.lineCount, line + SUFFIX_LINE_CNT),
    0
  );

  try {
    const prefix = doc.getText(new vscode.Range(startPos, position));
    const suffix = doc.getText(new vscode.Range(position, endPos));

    // console.log('prefix & suffix:', prefix, '<[|]>', suffix);
    return { prefix, suffix };
  } catch (error: any) {
    console.error('Cannot get doc text:', error, [startPos, position, endPos]);
    return { prefix: '', suffix: '' };
  }
};

const reportUserInput = async (item: ReportItem) => {
  reportIntervalMinutes.forEach(async (interval: number) => {
    const sleepTime = interval * 60 * 1000; // to milliseconds
    await sleep(sleepTime);

    const acceptedAt = getAcceptedAt(item.completionID);
    const { prefix, suffix } = getPrefixAndSuffixAtPosition(
      item.doc,
      item.position
    );
    if (!prefix || !suffix) {
      return;
    }
    const reportItem = { ...item };
    delete reportItem.doc; // no need to record doc in event
    reportItem.id = randomUUID();
    reportItem.prefix = prefix;
    reportItem.suffix = suffix;
    reportItem.reportIntervalMs = sleepTime;
    reportItem.acceptedAt = acceptedAt;
    reportCodeSnippet(reportItem);
  });
};

export const addCompletion = (
  doc: vscode.TextDocument,
  position: vscode.Position,
  completionID: string
) => {
  const { prefix, suffix } = getPrefixAndSuffixAtPosition(doc, position);
  if (!prefix || !suffix) {
    return;
  }
  const item: ReportItem = {
    doc,
    position,
    completionID,
    originalPrefix: prefix,
    originalSuffix: suffix,
    filename: doc.fileName,
    language: doc.languageId,
    createdAt: new Date().toISOString()
  };
  reportUserInput(item);
};
