import * as vscode from 'vscode';
import { getTracker } from './code-coverage-tracker';
import { acceptCompletionID, addCompletion } from './unaccepted-code-tracker';
import { AcceptedEvent } from '@/completion/inline';
import { consoleDebug } from '@/extension';
import { UserWrittenCodeTracker } from './user-written-code-tracker';
import { IS_RED_ZONE } from '@/judge_env';

export const enableTracker = async () => {
  const subscriptions = [
    vscode.workspace.onDidChangeTextDocument(async (e) => {
      UserWrittenCodeTracker.instance.onTextDocumentChange(e);
    }),
    vscode.workspace.onDidChangeTextDocument(async e => {
      getTracker(e.document.languageId).countTotalTokens(e);
    })
  ];
  return subscriptions;
};

export const onAccepted = (event: AcceptedEvent) => {
  if (IS_RED_ZONE) {
    return;
  }
  consoleDebug('tracking: on acceptance', event);
  acceptCompletionID(event.response.id);
  const start = event.range.start;
  const editor = vscode.window.activeTextEditor;
  if (!editor) {
    return;
  }
  const end = editor.selection.active;
  const codeRange = new vscode.Range(start, end);
  getTracker(editor.document.languageId).countAcceptedTokens(
    codeRange,
    event.choice.model || '',
    editor.document.getText(codeRange),
    editor.document.fileName
  );
};

export const onCompleting = (
  doc: vscode.TextDocument,
  position: vscode.Position,
  completionID: string
) => {
  addCompletion(doc, position, completionID);
};
