// unavoidable global variables
export interface VsCodeState {
  // Flag indicates intelli sense pop up is active or not
  // Adding this since VS Code intelliSense API does not expose this variable
  isIntelliSenseActive: boolean;
  // Flag indicates whether codebu<PERSON> is doing vscode.TextEditor.edit
  isEditing: boolean;
  // Timestamp of previous user edit
  lastUserModificationTime: number;
}

export const vsCodeState: VsCodeState = {
  isIntelliSenseActive: false,
  isEditing: false,
  lastUserModificationTime: 0
};
