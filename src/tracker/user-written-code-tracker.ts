import * as vscode from 'vscode';
import { persistentEvent } from '@/telemetry';

/**
 * This singleton class is mainly used for calculating the user written code
 * for active CodeBuddy users.
 * It reports the user written code per 5 minutes when the user is coding and using CodeBuddy features
 */
export class UserWrittenCodeTracker {
  private _userWrittenNewCodeCharacterCount: Map<string, number>;
  private _userWrittenNewCodeLineCount: Map<string, number>;
  private _inlineAcceptedCodeCharacterCount: Map<string, number>;
  private _inlineAcceptedCodeLineCount: Map<string, number>;
  private _qIsMakingEdits: boolean;
  private _timer?: NodeJS.Timer;
  private _codeBuddyUsageCount: number;
  private _lastCodeBuddyInvocationTime: number;
  private _inlinePendingCompletionTexts: string[];

  static #instance: UserWrittenCodeTracker;
  private static copySnippetThreshold = 50;
  private static resetCodeBuddyIsEditingTimeoutMs = 1 * 60 * 1000;
  private static defaultCheckPeriodMillis = 5 * 60 * 1000;

  private constructor() {
    this._userWrittenNewCodeLineCount = new Map<string, number>();
    this._userWrittenNewCodeCharacterCount = new Map<string, number>();
    this._inlineAcceptedCodeLineCount = new Map<string, number>();
    this._inlineAcceptedCodeCharacterCount = new Map<string, number>();
    this._codeBuddyUsageCount = 0;
    this._qIsMakingEdits = false;
    this._timer = undefined;
    this._lastCodeBuddyInvocationTime = 0;
    this._inlinePendingCompletionTexts = [];
  }

  public get inlinePendingCompletionTexts(): string[] {
    return this._inlinePendingCompletionTexts;
  }

  public set inlinePendingCompletionTexts(value: string[]) {
    this._inlinePendingCompletionTexts.push(...value);
    if (this.inlinePendingCompletionTexts.length > 3) {
      this._inlinePendingCompletionTexts = this.inlinePendingCompletionTexts.slice(-3);
    }
  }

  public static get instance() {
    return (this.#instance ??= new this());
  }

  public isActive(): boolean {
    return true;
  }

  public onCodeBuddyFeatureInvoked() {
    this._codeBuddyUsageCount += 1;
    this._lastCodeBuddyInvocationTime = performance.now();
  }

  public onCodeBuddyStartsMakingEdits() {
    this._qIsMakingEdits = true;
    this.onCodeBuddyFeatureInvoked();
  }

  public onCodeBuddyFinishesEdits() {
    this._qIsMakingEdits = false;
  }

  public getUserWrittenCharacters(language: string) {
    return this._userWrittenNewCodeCharacterCount.get(language) || 0;
  }

  public getUserWrittenLines(language: string) {
    return this._userWrittenNewCodeLineCount.get(language) || 0;
  }

  public getInlineAcceptedCharacters(language: string) {
    return this._inlineAcceptedCodeCharacterCount.get(language) || 0;
  }

  public getInlineAcceptedLines(language: string) {
    return this._inlineAcceptedCodeLineCount.get(language) || 0;
  }

  public reset() {
    this._userWrittenNewCodeLineCount = new Map<string, number>();
    this._userWrittenNewCodeCharacterCount = new Map<string, number>();
    this._inlineAcceptedCodeLineCount = new Map<string, number>();
    this._inlineAcceptedCodeCharacterCount = new Map<string, number>();
    this._codeBuddyUsageCount = 0;
    this._qIsMakingEdits = false;
    this._lastCodeBuddyInvocationTime = 0;
    if (this._timer !== undefined) {
      clearTimeout(this._timer);
      this._timer = undefined;
    }
  }

  public emitCodeContributions() {
    const languages = [...new Set([...this._userWrittenNewCodeCharacterCount.keys(), ...this._inlineAcceptedCodeCharacterCount.keys()])];
    for (const language of languages) {
      const lineCount = this.getUserWrittenLines(language);
      const charCount = this.getUserWrittenCharacters(language);
      const inlineAcceptedCharCount = this.getInlineAcceptedCharacters(language);
      const inlineAcceptedLineCount = this.getInlineAcceptedLines(language);
      if (charCount > 0 || inlineAcceptedCharCount > 0) {
        const details = {
          language: language,
          lang: language,
          timestamp: new Date(Date.now()),
          userWrittenCodeCharacterCount: charCount,
          userWrittenCodeLineCount: lineCount,
          inlineAcceptedCharacterCount: inlineAcceptedCharCount,
          inlineAcceptedLineCount: inlineAcceptedLineCount,
        };
        const event = persistentEvent({
          name: 'copilot:user-inline_accepted-and-written-code',
        });
        event.end({
          details: details,
        });
      }
    }
  }

  private tryStartTimer() {
    if (this._timer !== undefined) {
      return;
    }
    if (!this.isActive()) {
      console.debug('Skip emiting code contribution metric. Telemetry disabled or not logged in. ');
      this.reset();
      return;
    }
    const startTime = performance.now();
    this._timer = setTimeout(() => {
      try {
        const currentTime = performance.now();
        const delay: number = UserWrittenCodeTracker.defaultCheckPeriodMillis;
        const diffTime: number = startTime + delay;
        if (diffTime <= currentTime) {
          // if (this._codeBuddyUsageCount <= 0) {
          //   console.debug('Skip emiting code contribution metric. There is no active CodeBuddy usage. ');
          //   return;
          // }
          // if (this._userWrittenNewCodeCharacterCount.size === 0) {
          //   console.debug('Skip emiting code contribution metric. There is no new code added. ');
          //   return;
          // }
          this.emitCodeContributions();
        }
      } catch (e) {
        console.error(`Exception Thrown from CodeBuddyCodeGenTracker: ${e}`);
      } finally {
        this.reset();
      }
    }, UserWrittenCodeTracker.defaultCheckPeriodMillis);
  }

  private countNewLines(str: string) {
    return str.split('\n').length - 1;
  }

  private isLanguageSupported(language: string): boolean {
    return !!language;
  }

  public onTextDocumentChange(e: vscode.TextDocumentChangeEvent) {
    // do not count code written by CodeBuddy as user written code
    if (
      !this.isLanguageSupported(e.document.languageId) ||
      e.contentChanges.length === 0 ||
      this._qIsMakingEdits
    ) {
      // if the boolean of qIsMakingEdits was incorrectly set to true
      // due to unhandled edge cases or early terminated code paths
      // reset it back to false after a reasonable period of time
      if (this._qIsMakingEdits) {
        if (performance.now() - this._lastCodeBuddyInvocationTime > UserWrittenCodeTracker.resetCodeBuddyIsEditingTimeoutMs) {
          console.warn('Reset CodeBuddy is editing state to false.');
          this._qIsMakingEdits = false;
        }
      }
      return;
    }
    const contentChange = e.contentChanges[0];
    // if user copies code into the editor for more than 50 characters
    // do not count this as total new code, this will skew the data,
    // reporting highly inflated user written code
    const language = e.document.languageId;
    if (!language) {
      return;
    }
    if (this.inlinePendingCompletionTexts.includes(contentChange.text)) {
      const inlineCharCount = this.getInlineAcceptedCharacters(language);
      this._inlineAcceptedCodeCharacterCount.set(language, inlineCharCount + contentChange.text.length);
      const inlineLineCount = this.getInlineAcceptedLines(language);
      this._inlineAcceptedCodeLineCount.set(language, inlineLineCount + contentChange.text.split('\n').length);
    } else if (contentChange.text.length > UserWrittenCodeTracker.copySnippetThreshold) {
      return;
    } else {
      const charCount = this.getUserWrittenCharacters(language);
      this._userWrittenNewCodeCharacterCount.set(language, charCount + contentChange.text.length);
      const lineCount = this.getUserWrittenLines(language);
      this._userWrittenNewCodeLineCount.set(language, lineCount + this.countNewLines(contentChange.text));
    }
    // start 5 min data reporting once valid user input is detected
    this.tryStartTimer();
  }
}
