import * as vscode from 'vscode';
import { distance } from 'fastest-levenshtein';
import { persistentEvent } from '@/telemetry';
import { vsCodeState } from './vscode-state';

const defaultCheckPeriodMillis = 1000 * 60 * 5;

interface CompletionToken {
  range: vscode.Range;
  model: string;
  text: string;
  accepted: number;
}

/**
 * This singleton class is mainly used for calculating the code written by codebuddy
 */
export class CodeBuddyCodeCoverageTracker {
  private _acceptedTokens: { [key: string]: CompletionToken[] };
  private _totalTokens: { [key: string]: number };
  private _timer?: NodeJS.Timer;
  private _startTime: number;
  private _language: string;
  private _serviceInvocationCount: number;

  constructor(language: string) {
    this._acceptedTokens = {};
    this._totalTokens = {};
    this._startTime = 0;
    this._language = language;
    this._serviceInvocationCount = 0;
  }

  public get serviceInvocationCount(): number {
    return this._serviceInvocationCount;
  }

  public get acceptedTokens(): { [key: string]: CompletionToken[] } {
    return this._acceptedTokens;
  }

  public get totalTokens(): { [key: string]: number } {
    return this._totalTokens;
  }

  public isActive(): boolean {
    return true;
  }

  public countAcceptedTokens(
    range: vscode.Range,
    model: string,
    text: string,
    filename: string
  ) {
    if (!this.isActive()) {
      return;
    }
    // generate accepted recommendation token and stored in collection
    this.addAcceptedTokens(filename, {
      range: range,
      model: model,
      text: text,
      accepted: text.length
    });
  }

  public incrementServiceInvocationCount() {
    this._serviceInvocationCount += 1;
  }

  public flush() {
    if (!this.isActive()) {
      this._totalTokens = {};
      this._acceptedTokens = {};
      this.closeTimer();
      return;
    }
    try {
      this.emitCodeContribution();
    } catch (error) {
      console.error(
        `Encountered ${error} when emitting code contribution metric`
      );
    }
  }

  public updateAcceptedTokensCount(editor: vscode.TextEditor) {
    const filename = editor.document.fileName;
    if (filename in this._acceptedTokens) {
      for (let i = 0; i < this._acceptedTokens[filename].length; i++) {
        const oldText = this._acceptedTokens[filename][i].text;
        const newText = editor.document.getText(
          this._acceptedTokens[filename][i].range
        );
        this._acceptedTokens[filename][i].accepted =
          this.getUnmodifiedAcceptedTokens(oldText, newText);
      }
    }
  }
  // With edit distance, complicate usermodification can be considered as simple edit(add, delete, replace),
  // and thus the unmodified part of recommendation length can be deducted/approximated
  // ex. (modified > original): originalRecom: foo -> modifiedRecom: fobarbarbaro, distance=9, delta=12 - 9 = 3
  // ex. (modified == original): originalRecom: helloworld -> modifiedRecom: HelloWorld, distance=2, delta=10 - 2 = 8
  // ex. (modified < original): originalRecom: CodeWhisperer -> modifiedRecom: CODE, distance=12, delta=13 - 12 = 1
  public getUnmodifiedAcceptedTokens(origin: string, after: string) {
    return Math.max(origin.length, after.length) - distance(origin, after);
  }

  public emitCodeContribution() {
    let totalTokens = 0;
    for (const filename in this._totalTokens) {
      totalTokens += this._totalTokens[filename];
    }
    if (vscode.window.activeTextEditor) {
      this.updateAcceptedTokensCount(vscode.window.activeTextEditor);
    }
    let acceptedTokens: { [key: string]: number } = {};
    for (const filename in this._acceptedTokens) {
      this._acceptedTokens[filename].forEach(v => {
        if (
          filename in this._totalTokens &&
          this._totalTokens[filename] >= v.accepted
        ) {
          if (!(v.model in acceptedTokens)) {
            acceptedTokens[v.model] = 0;
          }
          acceptedTokens[v.model] += v.accepted;
        }
      });
    }
    const details = {
      totalTokens,
      language: this._language,
      acceptedTokens,
      successCount: this._serviceInvocationCount,
      timestamp: new Date(Date.now())
    };
    const event = persistentEvent({
      name: 'copilot:completion-rate'
    });
    event.end({
      details: details
    });
  }

  private tryStartTimer() {
    if (this._timer !== undefined) {
      return;
    }
    const currentDate = new Date();
    this._startTime = currentDate.getTime();
    this._timer = setTimeout(() => {
      try {
        const currentTime = new Date().getTime();
        const delay: number = defaultCheckPeriodMillis;
        const diffTime: number = this._startTime + delay;
        if (diffTime <= currentTime) {
          let totalTokens = 0;
          for (const filename in this._totalTokens) {
            totalTokens += this._totalTokens[filename];
          }
          if (totalTokens > 0) {
            this.flush();
          } else {
            console.debug(
              'CodeBuddyCodeCoverageTracker: skipped telemetry due to empty tokens array'
            );
          }
        }
      } catch (e) {
        console.log(`Exception Thrown from CodeBuddyCodeCoverageTracker: ${e}`);
      } finally {
        this.resetTracker();
        this.closeTimer();
      }
    }, defaultCheckPeriodMillis);
  }

  private resetTracker() {
    this._totalTokens = {};
    this._acceptedTokens = {};
    this._startTime = 0;
    this._serviceInvocationCount = 0;
  }

  private closeTimer() {
    if (this._timer !== undefined) {
      clearTimeout(this._timer);
      this._timer = undefined;
    }
  }

  public addAcceptedTokens(filename: string, token: CompletionToken) {
    if (!(filename in this._acceptedTokens)) {
      this._acceptedTokens[filename] = [];
    }
    this._acceptedTokens[filename].push(token);
  }

  public addTotalTokens(filename: string, count: number) {
    if (!(filename in this._totalTokens)) {
      this._totalTokens[filename] = 0;
    }
    this._totalTokens[filename] += count;
    if (this._totalTokens[filename] < 0) {
      this._totalTokens[filename] = 0;
    }
  }

  public countTotalTokens(e: vscode.TextDocumentChangeEvent) {
    // ignore no contentChanges. ignore contentChanges from other plugins (formatters)
    // only include contentChanges from user action.
    // Also ignore deletion events due to a known issue of tracking deleted codebuddy tokens.
    if (
      // !runtimeLanguageContext.isLanguageSupported(e.document.languageId) ||
      vsCodeState.isEditing ||
      e.contentChanges.length !== 1 ||
      e.contentChanges[0].text.length === 0
    ) {
      return;
    }
    const content = e.contentChanges[0];
    this.tryStartTimer();
    this.addTotalTokens(e.document.fileName, content.text.length);
  }
}

const instances = new Map<string, CodeBuddyCodeCoverageTracker>();

export const getTracker = (language: string): CodeBuddyCodeCoverageTracker => {
  let instance = instances.get(language);
  if (instance) {
    return instance;
  }
  instance = new CodeBuddyCodeCoverageTracker(language);
  instances.set(language, instance);
  return instance;
};
