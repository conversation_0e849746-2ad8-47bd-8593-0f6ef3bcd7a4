import {
  Disposable,
  ExtensionContext,
  Range,
  TextDocument,
  TextDocumentChangeEvent,
  TextEditorDecorationType,
  Uri,
  window,
  workspace
} from 'vscode';
import { getUpdatedRanges } from 'vscode-position-tracking';
import { FILE_HISTORIES_DIR } from '@/config';
import { randomUUID } from 'crypto';
import * as fse from 'fs-extra';
import dayjs from 'dayjs';
import * as Diff from 'diff';
import { telemetry } from '@/telemetry';
import { consoleDebug } from '@/extension';

let decorationType: TextEditorDecorationType | undefined = undefined;

const FILE_ERROR = 'file-error';

// FIXME: 采纳行：记录 Model
const DOC_RANGES: Map<Uri, Range[]> = new Map();
const FILE_HISTORIES: Map<Uri, string> = new Map();

const historiesDir = `${FILE_HISTORIES_DIR}/${dayjs().format('YYYYMMDD')}`;

function shouldIgnoreDoc(_doc: TextDocument): boolean {
  // TODO: 忽略：非特定语言
  // TODO: 忽略：非当前工作区文件
  // TODO: 忽略：gitignore
  return false;
}

export function addGeneratedRanges(doc: TextDocument, ranges: Range[]) {
  const uri = doc.uri;
  if (!DOC_RANGES.has(uri)) {
    if (shouldIgnoreDoc(doc)) {
      return;
    }
    DOC_RANGES.set(uri, []);
  }
  DOC_RANGES.get(uri)!.push(...ranges);
  updateDecorationsIfActive(doc);
}

function newHistoryFile(): string {
  return `${historiesDir}/${randomUUID()}.tmp`;
}

function cacheFileHistoryBeforeSaved(doc: TextDocument) {
  const uri = doc.uri;
  if (FILE_HISTORIES.has(uri)) {
    return;
  }
  if (shouldIgnoreDoc(doc)) {
    return;
  }
  const history = newHistoryFile();
  fse.ensureDirSync(historiesDir);
  try {
    fse.copyFileSync(uri.fsPath, history);
  } catch (error: any) {
    FILE_HISTORIES.set(uri, FILE_ERROR);
    console.warn(error);
    return;
  }
  FILE_HISTORIES.set(uri, history);
}

function cleanFileHistory(doc: TextDocument) {
  const uri = doc.uri;
  const history = FILE_HISTORIES.get(uri);
  if (!history || history === FILE_ERROR) {
    return;
  }
  fse.rmSync(history);
  FILE_HISTORIES.delete(uri);
}

function cleanDocRanges(doc: TextDocument) {
  const uri = doc.uri;
  DOC_RANGES.delete(uri);
  updateDecorationsIfActive(doc);
}

// TODO： 剔除 diff 没有 change 的行
function calcAcceptedLineCount(doc: TextDocument): number {
  const ranges = DOC_RANGES.get(doc.uri);
  if (!ranges) {
    return 0;
  }
  const linesSet = new Set(ranges.map(range => range.start.line));
  return linesSet.size;
}

function calcNewLineCount(doc: TextDocument): number {
  const history = FILE_HISTORIES.get(doc.uri);
  if (!history || history === FILE_ERROR) {
    return 0;
  }
  const historyContent = fse.readFileSync(history, { encoding: 'utf-8' });
  const newContent = fse.readFileSync(doc.uri.fsPath, { encoding: 'utf-8' });

  const changes = Diff.diffLines(historyContent, newContent, {
    ignoreWhitespace: true
  });

  return changes.reduce((acc, change) => {
    if (change.added && change.count) {
      acc += change.count;
    }
    return acc;
  }, 0);
}

function updateDecorationsIfActive(doc: TextDocument) {
  if (!decorationType) {
    return;
  }
  const editor = window.activeTextEditor;
  if (!editor) {
    return;
  }
  if (editor.document !== doc) {
    return;
  }
  if (shouldIgnoreDoc(doc)) {
    return;
  }
  const ranges = DOC_RANGES.get(doc.uri);
  editor.setDecorations(decorationType, ranges || []);
}

function onDidChangeTextDocument(event: TextDocumentChangeEvent) {
  if (event.contentChanges.length === 0) {
    return;
  }
  const doc = event.document;
  cacheFileHistoryBeforeSaved(doc);

  const uri = doc.uri;
  const ranges = DOC_RANGES.get(uri);
  if (!ranges) {
    return;
  }

  // Merge ranges
  const merged: Range[] = getUpdatedRanges(
    ranges,
    event.contentChanges as any,
    {
      onDeletion: 'shrink',
      onAddition: 'split'
    }
  );
  DOC_RANGES.set(uri, merged);
  updateDecorationsIfActive(doc);
}

function onDidSaveTextDocument(doc: TextDocument) {
  if (shouldIgnoreDoc(doc)) {
    return;
  }
  const newLines = calcNewLineCount(doc);
  const acceptedLines = calcAcceptedLineCount(doc);
  consoleDebug(
    `Saved file ${newLines} new lines, ${acceptedLines} accepted lines`,
    workspace.asRelativePath(doc.uri)
  );
  cleanDocRanges(doc);
  cleanFileHistory(doc);

  telemetry().offer({
    name: 'editor:file-saved',
    refer: workspace.asRelativePath(doc.uri),
    details: {
      newLines,
      acceptedLines
    }
  });
}

export async function initDocTracker(
  _context: ExtensionContext
): Promise<Disposable[]> {
  decorationType = window.createTextEditorDecorationType({
    backgroundColor: 'rgba(0,166,255,0.10)'
  });

  // FIXME: 清理历史文件（>=7 天）

  return [
    {
      dispose(): any {
        decorationType = undefined;
      }
    },
    workspace.onDidSaveTextDocument(onDidSaveTextDocument),
    workspace.onDidChangeTextDocument(onDidChangeTextDocument)
  ];
}
