import { agent, CompletionResponse } from '@/agent';
import { consoleDebug, PLU<PERSON>NS_INSTALLED } from '@/extension';
import * as vscode from 'vscode';
interface EventEntry {
  startTime: number;
  id: string;
  requestId: string;
  requestDelayMillis: number;
}

export interface CompletionTrackerEntry {
  response: CompletionResponse;
  requestDelayMillis: number;
}

export class CompletionShownTracker {
  private showTime: number;
  private waitTime: number;
  private events: EventEntry[];
  private timeoutId: NodeJS.Timeout | undefined;

  public constructor(showTime: number) {
    this.showTime = showTime;
    this.waitTime = showTime * 3;
    this.events = [];
    this.timeoutId = undefined;
  }

  private clearTimeout() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = undefined;
    }
  }

  public emitAccepted(id: string) {
    this.clearTimeout();
    const filteredEvent = this.events.filter(e => e.id === id);
    if (filteredEvent.length > 0) {
      this.emit(Date.now(), filteredEvent[0], 'shown');
    }
    this.events = [];
  }

  private emit(nowMillis: number, entry: EventEntry, type: 'shown' | 'ignore') {
    const details = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      request_delay_millis: entry.requestDelayMillis
    };

    Object.keys(PLUGINS_INSTALLED).forEach(key => {
      // @ts-ignore
      details[`plugin_${key}`] = PLUGINS_INSTALLED[key];
    });

    // add cursor/windsurf
    const appName = vscode.env.appName;
    // @ts-ignore
    details['app_name'] = appName;

    agent().emitCompletionEvent({
      id: entry.id,
      requestId: entry.requestId,
      code: type,
      time: entry.startTime,
      du: nowMillis - entry.startTime,
      details
    });
    consoleDebug('Completion shown:', entry);
  }

  public emitAll() {
    this.clearTimeout();
    const nowMillis = Date.now();
    for (let event of this.events) {
      this.emit(
        nowMillis,
        event,
        nowMillis - event.startTime >= this.showTime ? 'shown' : 'ignore'
      );
    }
    this.events = [];
  }

  public take(entry: CompletionTrackerEntry) {
    const resp = entry.response;
    this.events.push({
      id: resp.id,
      requestId: resp.requestId,
      startTime: Date.now(),
      requestDelayMillis: entry.requestDelayMillis
    });
    this.clearTimeout();
    this.timeoutId = setTimeout(() => {
      this.emitAll();
    }, this.waitTime);
  }
}
