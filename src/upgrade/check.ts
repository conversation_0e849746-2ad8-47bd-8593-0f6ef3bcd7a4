import { notify } from '@/notify';
import { agentVersion } from '@/agent/binary';
import * as semver from 'semver';
import { SemVer } from 'semver';
import { appChannel, appVersion, currentUser } from '@/extension';
import {
  AGENT_VERSION_RANGE,
  ARTIFACT_ALLOW_LIST_URL,
  ARTIFACT_GRAPHQL_API,
  ARTIFACT_GROUP,
  CHANNEL_DEV,
  COMPONENT_CONFIG,
  AGENT_CONFIG_VERSION_PATH,
  COMP_CODE_FACTORY,
  CODEFACTORY_DOWNLOAD_URL,
  COMP_CODE_AGENT,
  COMP_CODE_PLUGIN
} from '@/config';
import axios from 'axios';
import { channelFromVersion, splitLines } from '@/utils';
import { downloadToString } from '@/utils/download';
import vscode from 'vscode';
import { timer } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { features } from '@/feat';
import { CMD_AGENT_UPGRADE_TO_VERSION } from '@/commands';
import { CMD_CODE_FACTORY_UPGRADE_TO_VERSION, CMD_PLUGIN_UPGRADE_TO_VERSION } from '@/upgrade/upgrade';
import { string } from 'yaml/dist/schema/common/string';
import { URLSearchParams } from 'url';
import { envManager, isRedZone } from '@/judge_env';

export let CODEFACTORY_VERSION: string | undefined;

export type Component = 'agent' | 'vscode' | 'codefactory';
export const COMPONENT_NAME_MAP: Record<Component, string> = {
  agent: 'codebuddy_agent',
  vscode: 'codebuddy',
  codefactory: 'codefactory'
};

export function codefactoryVersion(): string | undefined {
  return CODEFACTORY_VERSION;
}
export const setCodeFactoryVersion = (version: string | undefined) => {
  CODEFACTORY_VERSION = version;
};
const UPGRADE_CHECK_DELEY_MS = 5 * 1000;
const UPGRADE_CHECK_INTERVAL_MS = 30 * 60 * 1000; // every 30 minutes


const paths = [
  '/consts/codebuddy/versions/model_param_groups.yaml',
	'/consts/codebuddy/versions/user_groups.yaml',
	'/consts/codebuddy/versions/agent_00002.redzone.yaml',
];
export async function checkUpgrade(component: Component, silent?: boolean) {
  if (silent && component === 'agent' && agentVersion() === undefined) {
    console.log('Ignore selint upgrade checking for agent: agent is not started/ready.');
    return;
  }

  let latest = await getAvailableUpgradeVersion(component);
  if (!latest) {
    console.log('No available upgrade.');
    if (!silent) {
      notify.whenNoUpgrade(
        component,
        (component === 'agent' ? agentVersion() :
          component === 'vscode' ? appVersion() :
            codefactoryVersion()) || 'Unknown'
      );
    }
    return;
  }

  console.log(`Found available upgrade: ${latest}`);

  if (features.forceAutoUpgrade(component)) {
    console.log('Auto upgrading...');
    upgrade(component, latest, silent);
  } else {
    notify.whenUpgradeAvailable(component, latest);
  }
}

function upgrade(component: Component, version: string, silent?: boolean) {
  let cmd: string;
  switch (component) {
    case 'agent':
      cmd = CMD_AGENT_UPGRADE_TO_VERSION;
      break;
    case 'vscode':
      cmd = CMD_PLUGIN_UPGRADE_TO_VERSION;
      break;
    case 'codefactory':
      cmd = CMD_CODE_FACTORY_UPGRADE_TO_VERSION;
      break;
  }
  vscode.commands.executeCommand(cmd, version, silent);
}

export async function getAvailableUpgradeVersion(
  component: Component
): Promise<string | null> {
  const latest = await getAvailableLatestVersion(component);
  return latest && isUpgrade(component, latest) ? latest : null;
}

export function getCurVersion(component: Component | string): string | undefined {
  let curVersion;
  switch (component) {
    case 'agent':
    case COMP_CODE_AGENT:
      curVersion = agentVersion();
      break;
    case 'vscode':
    case COMP_CODE_PLUGIN:
      curVersion = appVersion();
      break;
    case 'codefactory':
    case COMP_CODE_FACTORY:
      curVersion = codefactoryVersion();
      break;
  }
  return curVersion;
}

export function isUpgrade(component: Component, version: string): boolean {
  const curVersion = getCurVersion(component);
  if (!version || curVersion === version) {
    return false;
  }

  console.log(`IsUpgrade: current: ${curVersion} -> version: ${version}`);
  return curVersion === undefined || semver.gt(version, curVersion);
}

export async function getAvailableLatestVersion(
  component: Component
): Promise<string | null> {
  // for dev
  if (appChannel() === CHANNEL_DEV && component === 'codefactory') {
    return getLatestVersion();
  }

  let remoteVersions: string[] = await listRemoteVersions(component);

  const compVers = await sortedAvailableVersions(component, remoteVersions);
  if (compVers.length === 0) {
    return null;
  }
  console.log(`Latest available version for [${component}]: ${compVers[0]}`);
  return compVers[0];
}
// for codefactory now
async function getLatestVersion(): Promise<string | null> {
  const latestVersionUrl = `${CODEFACTORY_DOWNLOAD_URL}/latest_version.txt?_ts=${Date.now()}`;
  const response = await axios.get(latestVersionUrl);

  const trimmedVersion = response.data.trim();
  return trimmedVersion.startsWith('v') ? trimmedVersion.substring(1) : trimmedVersion;
}

async function sortedAvailableVersions(
  comp: Component,
  raw: string[]
): Promise<string[]> {
  console.log('SortedAvailableVersions input:', raw);
  let versions: SemVer[] = raw
    .map(version => semver.parse(version))
    .filter(version => version !== null) as SemVer[];

  return versions
    .sort((a, b) => semver.compare(b, a))
    .filter(availableVersionFn(comp))
    .map(version => version.raw);
}

function availableVersionFn(comp: Component): (v: SemVer) => boolean {
  if (comp === 'codefactory') {
    return _ => true;
  }
  if (comp === 'agent') {
    const { start, end } = AGENT_VERSION_RANGE;
    return v => semver.gte(v, start) && semver.lte(v, end);
  }

  const curr = semver.parse(appVersion());
  if (curr === null) {
    return _ => false;
  }
  return v => {
    return semver.gte(v, curr);
  };
}

async function listRemoteVersions(
  component: Component
): Promise<string[]> {
  const componentName = COMPONENT_NAME_MAP[component];
  const userName = await currentUser();
  const params = new URLSearchParams();
  params.append('component', componentName);
  params.append('username', userName);

  if (isRedZone()) {
    for (const path of paths) {
      params.append('paths', path);
    }
  }

  try {
    const response = await axios.post(
      envManager.getPluginVersionUrl,
      params.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    // sometimes version
    const version = response.data?.version?.version || response.data?.version?.versions;
    console.log(`${componentName} get remote versions: ${version}`);
    console.log('Fetched remote versions:', version);
    return Array.isArray(version) ? version : [version];
  } catch (e) {
    console.log(`[CodeBuddy] listRemoteVersions error: ${e}`);
    return [];
  }
}
export async function checkUpgradeAfterActivated(): Promise<vscode.Disposable[]> {
  const subscribe = timer(UPGRADE_CHECK_DELEY_MS, UPGRADE_CHECK_INTERVAL_MS)
    .pipe(
      switchMap(() => {
        return Promise.all([
          checkUpgrade('agent', true),
          checkUpgrade('vscode', true),
          checkUpgrade('codefactory', true),
        ]);
      })
    ).subscribe({
      error(e) {
        console.error('Upgrade check error:', e);
      },
      next() {
        console.log('Upgrade check done');
      }
    });

  return [{
    dispose(): any {
      subscribe.unsubscribe();
    }
  }];
}
