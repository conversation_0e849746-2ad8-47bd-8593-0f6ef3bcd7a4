import * as vscode from 'vscode';

import { getCopilotConf } from '@/config';
import { nextEditIsEditable } from '@/context/setContext';
import { getControlKey } from '@/utils/os';


export function onDidChangeTextEditorSelectionTriggerInlineChat(
    _context: vscode.ExtensionContext
) {
    // 创建装饰类型
    const decorationType = vscode.window.createTextEditorDecorationType({
        after: {
            contentText: ' 按下 ' + getControlKey() + ' + i 键，使用 CodeFactory 优化代码',
            color: '#888888',
            fontStyle: 'italic',
            margin: '0 0 0 1em'
        }
    });
    // 监听选中文本变化
    const selectionChangeDisposable = vscode.window.onDidChangeTextEditorSelection((event) => {
        const config = getCopilotConf();
        const inlineChatEnabled = config.get<boolean>('inlineChat.enabled', false);
        if (!inlineChatEnabled) {
            return;
        }
        if (nextEditIsEditable) {
            return;
        }
        const editor = event.textEditor;
        const selection = editor.selection;
        // 清除之前的装饰
        editor.setDecorations(decorationType, []);
        // 如果有选中文本
        if (!selection.isEmpty) {
            // const selectedText = editor.document.getText(selection);
            // 在选中文本末尾添加装饰
            const startPosition = selection.start;
            const decoration = {
                range: new vscode.Range(
                    new vscode.Position(startPosition.line, 9999),
                    new vscode.Position(startPosition.line, 9999)
                )
                // hoverMessage: ''
            };
            editor.setDecorations(decorationType, [decoration]);
        }
    });
    _context.subscriptions.push(decorationType, selectionChangeDisposable);
}
