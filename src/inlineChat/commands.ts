import * as vscode from 'vscode';


export const inlineChatCommand = 'li.codebuddy.chat.inline-chat';


export async function registerInlineChatCommands(
    _context: vscode.ExtensionContext
): Promise<vscode.Disposable[]> {
    return [
        vscode.commands.registerCommand(inlineChatCommand, async () => {
            let userInput = await vscode.window.showInputBox({
                prompt: '',
                placeHolder: '请输入描述优化代码',
            });
            vscode.commands.executeCommand('code-factory.improveCode', { 'codebuddy': true, userInput });
        }),
    ];
}
