const redZoneKeyWord = 'redzone';

export function appEnv(): string {
    // process.env.CI_COMMIT_TAG
    // v1.5.0-alpha+redzone
    // v1.5.0-alpha.1+redzone
    // v1.5.0+redzone
    // v1.5.0+redzone.1
    const ciCommitTag = process.env.CI_COMMIT_TAG;
    if (ciCommitTag) {
        const [_, build] = ciCommitTag.split('+');
        return build || '';
    }
    return '';
}

export function isRedZone(): boolean {
    const e = appEnv();
    return e.includes(redZoneKeyWord);
}

interface EnvManager {
    getPluginVersionUrl: string
    codeSnippetReportURL: string
    telemetryUrl: string
    artifactHost: string
    s3Host: string
    defaultCopilotApi: string
    defaultTelemetryApi: string
    portalBasePath: string
    idaasBasePath: string
}

const redZoneEnvManager: EnvManager = {

    // TODO
    // 公共红区部署的 copilot 服务
    getPluginVersionUrl: 'https://coding-copilot-redzone.ep.chehejia.com/api/copilot/plugin/version',

    // 需要红区把 portal-k8s-prod-redzone.ep.chehejia.com 解析之后的 ip 的请求转到公共红区
    codeSnippetReportURL: 'https://portal-k8s-prod-redzone.ep.chehejia.com/webhook-receiver/v1.0/invoke/webhook-receiver/method/webhook-receiver?uuid=6cd24c04-40d5-4aba-ab3e-0911beec7ed9&name=codebuddy-completion',
    telemetryUrl: 'https://portal-k8s-prod-redzone.ep.chehejia.com/webhook-receiver/v1.0/invoke/webhook-receiver/method/webhook-receiver?uuid=d9000b78-4571-11ef-9190-32b8cfa558c5&name=codebuddy-telemetry-events-01',
    artifactHost: 'https://gitlabee-redzone.ep.chehejia.com', // TODO, 移除 gitlabee 依赖
    s3Host: 'https://s3-redzone.ep.chehejia.com',
    defaultCopilotApi: 'https://coding-copilot-redzone.ep.chehejia.com/api/copilot',
    defaultTelemetryApi: 'https://codebuddy-telemetry-prod-redzone.ep.chehejia.com',
    portalBasePath: 'https://portal-k8s-prod-redzone.ep.chehejia.com',
    idaasBasePath: 'https://id-lixiang-redzone.ep.chehejia.com/api',
};

const commonEnvManager: EnvManager = {
    getPluginVersionUrl: 'https://portal-k8s-prod.ep.chehejia.com/api/copilot/plugin/version',

    codeSnippetReportURL: 'https://portal-k8s-prod.ep.chehejia.com/webhook-receiver/v1.0/invoke/webhook-receiver/method/webhook-receiver?uuid=6cd24c04-40d5-4aba-ab3e-0911beec7ed9&name=codebuddy-completion',

    telemetryUrl: 'https://portal-k8s-prod.ep.chehejia.com/webhook-receiver/v1.0/invoke/webhook-receiver/method/webhook-receiver?uuid=d9000b78-4571-11ef-9190-32b8cfa558c5&name=codebuddy-telemetry-events-01',
    artifactHost: 'https://gitlabee.chehejia.com',
    s3Host: 'https://s3.ep.chehejia.com',
    defaultCopilotApi: 'https://portal-k8s-prod.ep.chehejia.com/api/copilot',
    defaultTelemetryApi: 'https://codebuddy-telemetry-prod.ep.chehejia.com',
    portalBasePath: 'https://portal-k8s-prod.ep.chehejia.com',
    idaasBasePath: 'https://id.lixiang.com/api',
};

export function getEnvManager(): EnvManager {
    if (isRedZone()) {
        return redZoneEnvManager;
    }
    return commonEnvManager;
}

export const envManager = getEnvManager();
export const IS_RED_ZONE = isRedZone();
export const APP_ENV = appEnv();
