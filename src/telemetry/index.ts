import { Disposable, ExtensionContext } from 'vscode';
import { agent, agentVersion } from '@/agent';
import { mergeIfAbsent } from '@/utils';
import axios from 'axios';
import { appVersion, appChannel } from '@/extension';
import { MENU_STATE } from '@/menu';
import { envManager } from '@/judge_env';

export interface Telemetry {
  offer(form: EventForm): void;
}

export function telemetry(): Telemetry {
  return agent();
}

export async function initTelemetry(
  _context: ExtensionContext
): Promise<Disposable[]> {
  return [];
}

export interface EventForm {
  name: string;
  time?: number;
  du?: number;
  refer?: string;
  copilotModel?: string;
  details?: { [key: string]: any };
}

export class PersistentEvent {
  private _time: number;
  private _event: EventForm;

  constructor(event: EventForm) {
    this._event = event;
    this._time = event.time || Date.now();
  }

  public get time(): number {
    return this._time;
  }

  public rename(name: string): void {
    this._event.name = name;
  }

  public end(form?: Partial<EventForm>): void {
    const e = this._event;
    if (form) {
      mergeIfAbsent(e, form);
    }
    e.time = this._time;
    e.du = Date.now() - this._time;
    telemetry().offer(e);
  }
}

export function persistentEvent(event: EventForm) {
  return new PersistentEvent(event);
}

export interface TelemetryEvent {
  name?: string;
  time?: number;
  refer?: string;
  plugin_version?: string;
  plugin_channel?: string;
  platform_agent_version?: string;
  user_name?: string;
  user_email?: string;
  details?: any;
}
export function sendEvent(event: TelemetryEvent) {
  const _event: TelemetryEvent = {
    time: Date.now(),
    plugin_version: appVersion(),
    plugin_channel: appChannel(),
    platform_agent_version: agentVersion(),
  };
  const auth = MENU_STATE.getValue().auth;
  if (auth.authorized) {
    event.user_name = auth.account?.nickname;
    event.user_email = auth.account?.email;
  }
  mergeIfAbsent(event, _event);
  axios.post(envManager.telemetryUrl, event);
}
