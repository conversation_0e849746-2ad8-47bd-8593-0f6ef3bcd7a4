import { appChannel } from '@/extension';
import { CHANNEL_ALPHA, CHANNEL_BETA, CHANNEL_STABLE } from '@/config';
import { Component } from '@/upgrade/check';

export const features = {
  forceAutoUpgrade(cmpt: Component) {
    switch (cmpt) {
      case 'agent':
        return features.forceAutoUpgradeAgent();
      case 'vscode':
        return features.forceAutoUpgradePlugin();
      case 'codefactory':
        return features.forceAutoUpgradeCodeFactory();
      default:
        return false;
    }
  },

  forceAutoUpgradePlugin() {
    switch (appChannel()) {
      case CHANNEL_ALPHA:
      case CHANNEL_BETA:
      case CHANNEL_STABLE:
        return true;
      default:
        return false;
    }
  },

  forceAutoUpgradeAgent() {
    switch (appChannel()) {
      case CHANNEL_ALPHA:
      case CHANNEL_BETA:
      case CHANNEL_STABLE:
        return true;
      default:
        return false;
    }
  },

  forceAutoUpgradeCodeFactory() {
    return true;
  },
};
