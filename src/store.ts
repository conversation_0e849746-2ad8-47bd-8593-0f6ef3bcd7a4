import { CONFIG_DIR, VSCODE_CODE } from './config';
import * as path from 'path';
import * as fse from 'fs-extra';
import { deepClone, mergeIfAbsent } from './utils';
import { Disposable, ExtensionContext } from 'vscode';

export type HistoryData = {
  versions: { [key: string]: { time: number } };
};

// ms
export const userInstalledExtensionLastReportTimeKey = 'u44c0665a8dc9405db6a8e940ebe4f647';

export type LocalStoredData = {
  histories: { [platform: string]: HistoryData };
  codeBuddySessions: any[];
  // 用户可见，所以用 uuid 表示 key，用来存储上报用户安装插件列表的时间戳
  [userInstalledExtensionLastReportTimeKey]: number | undefined;
};

export interface LocalStore {
  data: LocalStoredData;

  load(): Promise<void>;

  save(): Promise<void>;
}

const dataFile = path.join(CONFIG_DIR, 'vscode/config.json');

const DATA_DEFAULTS: LocalStoredData = {
  histories: {
    [VSCODE_CODE]: {
      versions: {}
    }
  },
  codeBuddySessions: [],
  [userInstalledExtensionLastReportTimeKey]: undefined,
};

async function prepare(): Promise<void> {
  fse.ensureFileSync(dataFile);
}

async function load(): Promise<LocalStoredData> {
  await prepare();
  const data = (await fse.readJson(dataFile, { throws: false })) || {};
  mergeIfAbsent(data, DATA_DEFAULTS);
  return data;
}

async function save(data: Partial<LocalStoredData>): Promise<void> {
  await prepare();
  await fse.outputJson(dataFile, data);
}

export const localStore: LocalStore = (() => {
  return {
    data: deepClone(DATA_DEFAULTS),
    // 这里每次都会读磁盘，不能 cache
    load: async function () {
      this.data = await load();
    },
    save: async function () {
      await save(this.data);
    }
  };
})();

export async function initLocalStore(
  _context: ExtensionContext
): Promise<Disposable[]> {
  await localStore.load();
  return [];
}
