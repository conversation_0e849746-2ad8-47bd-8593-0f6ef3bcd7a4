import { createStore } from 'zustand/vanilla'

// 定义状态接口
export interface AppState {
  status: 'loading' | 'ready' | 'error';
  isLogined: boolean | null;
}

// 定义状态更新接口
export interface AppStateActions {
  setStatus: (status: AppState['status']) => void;
  setIsLogined: (isLogined: boolean | null) => void;
  reset: () => void;
}

// 初始状态
const initialState: AppState = {
  status: 'loading',
  isLogined: null,
}

// 创建store
export const appStore = createStore<AppState & AppStateActions>()((set) => ({
  ...initialState,

  // 更新状态的方法
  setStatus: (status) => set({ status }),
  setIsLogined: (isLogined) => set({ isLogined }),
  reset: () => set(initialState),
}))

// 获取状态
export const getAppState = () => appStore.getState()

// 订阅状态变化

export const subscribeAppState = (
  listener: (state: AppState) => void,
  selector?: (state: AppState) => any
) => {
  // 如果提供了选择器，只有当选择器的结果改变时才调用监听器
  if (selector) {
    let currentSlice = selector(appStore.getState())
    return appStore.subscribe((state) => {
      const nextSlice = selector(state)
      if (nextSlice !== currentSlice) {
        currentSlice = nextSlice
        listener(state)
      }
    })
  }

  // 否则，每次状态改变都调用监听器
  return appStore.subscribe(listener)
}
