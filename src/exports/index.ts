import { commands } from 'vscode'
import { CodeBuddyAPI } from './codebuddy';
import {
  CMD_AUTH_LOGIN,
} from '@/commands'
import { EventForm, persistentEvent } from '@/telemetry';
import { appStore, getAppState, subscribeAppState } from '@/state/state';
import { isRedZone } from '@/judge_env';


const login = () => {
  commands.executeCommand(CMD_AUTH_LOGIN)
}

const actions = {
  login,
}

export function createCodeBuddyAPI(): CodeBuddyAPI {
  const api: CodeBuddyAPI = {
    isRedZone: isRedZone,
    actions,
    appStore,
    getAppState,
    subscribeAppState,
    telemetryEvent: async (event: EventForm) => {
      const e = persistentEvent(event);
      e.end();
    },
  };
  return api;
}
