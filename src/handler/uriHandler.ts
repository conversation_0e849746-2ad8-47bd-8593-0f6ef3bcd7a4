import * as vscode from 'vscode';

export class UriHandlerImpl implements vscode.UriHandler {
    private routeMap = new Map<string, URIHandler>();

    registerHandler(path: string, handler: URIHandler) {
        this.routeMap.set(path, handler);
    }

    unregisterHandler(path: string) {
        this.routeMap.delete(path);
    }

    handleUri(uri: vscode.Uri): vscode.ProviderResult<void> {
        const handler = this.routeMap.get(uri.path);
        if (!handler) {
            console.log('[CodeBuddy] Unknown URI path');
            return;
        }
        return handler.handle(uri);
    }
}

export interface URIHandler {
    handle(uri: vscode.Uri): Promise<void>
}