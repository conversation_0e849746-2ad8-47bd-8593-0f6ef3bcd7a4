import * as vscode from 'vscode';
import { URIHandler } from './uriHandler';
import { AuthResult } from '@/auth/types';
import { IDaasAuthProvider } from '@/auth/auth_provider';
import { agent } from '@/agent';
import { AuthStatus } from '@/agent/lsp-client';

export class AuthHandler implements URIHandler {
    private _authProvider?: IDaasAuthProvider;

    constructor(private context: vscode.ExtensionContext) {}

    async bindAuthProvider(authProvider: IDaasAuthProvider) {
        this._authProvider = authProvider;
    }

    async handle(uri: vscode.Uri): Promise<void> {
        if (!this._authProvider) {
            throw new Error('authHandler bind authProvider: authProvider doesnt init');
        }

        if (uri.path === '/auth/login/callback') {
            try {
                await this.handleAuthCallBack(uri);
            } catch (e) {
                if (this._authProvider) {
                    this._authProvider.rejectAuth(e instanceof Error ? e : new Error(String(e)));
                } else {
                    throw e;
                }
            }
        }
    }

    async handleAuthCallBack(uri: vscode.Uri): Promise<void> {
        try {
            const params = new URLSearchParams(uri.query);
            if (params.get('error')) {
                console.log('[CodeBuddy] 认证失败', params.get('error'));
                throw new Error(params.get('error') || '');
            }

            if (!this._authProvider) {
                throw new Error('authProvider is null');
            }

            const validState = this._authProvider.validateState(params.get('state') || '');
            if (!validState) {
                throw new Error('invalid state');
            }

            const code = params.get('code');
            if (!code) {
                throw new Error('code is empty');
            }

            // code换取凭证
            const tokenResponse = await this._authProvider.codeChallengeForToken(code);
            if (!tokenResponse || !tokenResponse.access_token) {
                throw new Error('get access_token error');
            }
            const account = await this._authProvider.getUserInfo(tokenResponse.access_token, tokenResponse.id_token);

            const authResult: AuthResult = {
                idToken: tokenResponse.id_token,
                accessToken: tokenResponse.access_token,
                refreshToken: tokenResponse.refresh_token,
                expiresAt: Date.now() + tokenResponse.expires_in * 1000,
                account: account
            };
            const authStatus: AuthStatus = {
                authorized: true,
                account: {
                    openid: account.openid,
                    nickname: account.nickname,
                    email: account.email,
                }
            };
            agent().emit_auth(authStatus);
            this._authProvider.resolveAuth(authResult);
        } catch(e) {
            if (this._authProvider) {
                this._authProvider.rejectAuth(e instanceof Error ? e : new Error(String(e)));
            } else {
                throw e;
            }
        }
    }
}
