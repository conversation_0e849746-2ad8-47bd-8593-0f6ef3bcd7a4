import axios from 'axios';
import { appChannel, appVersion } from '@/extension';
import { envManager } from '@/judge_env';

export interface CodeSnippet {
  id?: string;
  originalPrefix: string;
  originalSuffix: string;
  prefix?: string;
  suffix?: string;

  filename: string;
  language: string;
  createdAt: string;
  reportIntervalMs?: number; // 上报间隔，单位ms

  pluginVersion?: string;
  pluginChannel?: string;
}

export const reportCodeSnippet = (code: CodeSnippet) => {
  code.pluginVersion = appVersion();
  code.pluginChannel = appChannel();
  axios.post(envManager.codeSnippetReportURL, code);
};
