import { Account, auxResponse, IDaaSTokenResponse } from '@/auth/types';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';
import { envManager } from '@/judge_env';

export const CLIENT_ID = '1zbqafT2y3EFAR27EC60PO';
export const EP_SERVICE_ID = '6mt29Qp4vvGcnkMu4gVwwh';
export const EP_SERVICE_TEST_ID = 'Qb2q8KCX52BujCxQovcA5';

export const authRefreshToken = async (refreshToken: string): Promise<IDaaSTokenResponse> => {
    const tokenParams = new URLSearchParams([
        ['grant_type', 'refresh_token'],
        ['refresh_token', refreshToken],
    ]);

    const res = await axios.post<IDaaSTokenResponse>(
        `${envManager.idaasBasePath}/token`,
        tokenParams.toString(),
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'

            },
        },
    );
    return res.data;
};

export const codeChallengeForToken = async (code: string, codeVerifier: string): Promise<IDaaSTokenResponse> => {
    const tokenParams = new URLSearchParams([
        ['grant_type', 'authorization_code'],
        ['code', code],
        ['code_verifier', codeVerifier],
    ]);

    const response = await axios.post<IDaaSTokenResponse>(
        `${envManager.idaasBasePath}/token`,
        tokenParams.toString(),
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
    );
    return response.data;
};

export const getUserInfo = async (accessToken: string, idToken: string): Promise<Account> => {
    const res = await axios.get(
        `${envManager.portalBasePath}/api/v2/user/userinfo`,
        {
            headers: {
                'IDaaSAccessToken': accessToken,
            }
        }
    );

    const decoded = jwtDecode(idToken) as any;
    const account: Account = {
        id: decoded.sub,
        label: decoded.nickname,
        openid: decoded.sub,
        nickname: decoded.nickname,
        email: res.data.Email
    };
    return account;
};


// device code flow
export const aux = async ():Promise<auxResponse> => {
    const tokenParams = new URLSearchParams([
        ['client_id', CLIENT_ID],
        ['scope', 'api:all openid email profile offline_access'],
        ['audience', EP_SERVICE_ID],
    ]);

    const res = await axios.post<auxResponse>(
        `${envManager.idaasBasePath}/aux`,
        tokenParams.toString(),
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'

            },
        },
    );

    console.log(`[aux] ${JSON.stringify(res.data)}`);
    return res.data;
};

export const deviceCodeChangeToken = async (deviceCode: string): Promise<IDaaSTokenResponse> => {
    const tokenParams = new URLSearchParams([
        ['grant_type', 'urn:ietf:params:oauth:grant-type:device_code'],
        ['device_code', deviceCode],
    ]);

    const response = await axios.post<IDaaSTokenResponse>(
        `${envManager.idaasBasePath}/token`,
        tokenParams.toString(),
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
    );
    return response.data;
};