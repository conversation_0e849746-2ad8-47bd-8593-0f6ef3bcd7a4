import * as vscode from 'vscode';
import { cleanCodeDiffDecorations } from '@/utils/vscode/text-editor-decoration';
import { editManager } from './editManager';
import { updateBlockEditContext } from '@/context/setContext';

export type CurrentShownEditData = {
    editor: vscode.TextEditor | undefined,
    range: vscode.Range | undefined,
    text: string,
};


export const currentShownEdit: CurrentShownEditData = {
    editor: undefined,
    range: new vscode.Range(0, 0, 0, 0),
    text: '',
};

export function setCurrentShownEdit(editor: vscode.TextEditor | undefined, range: vscode.Range | undefined, text: string) {
    currentShownEdit.editor = editor;
    currentShownEdit.range = range;
    currentShownEdit.text = text;
}

export function acceptOrRejectEdit(isAccept: boolean) {
    if (!currentShownEdit.editor || !currentShownEdit.range) {
        return;
    }
    const editor = currentShownEdit.editor;
    if (isAccept) {
        editor.edit(editBuilder => {
            if (!currentShownEdit.range) { return; }
            // The replace method handles both insertion (if range is empty)
            // and replacement (if range is not empty).
            editBuilder.replace(currentShownEdit.range, currentShownEdit.text);
        });
        editManager.acceptedEdits = [currentShownEdit.text];
    }
    editor.selection = new vscode.Selection(editor.selection.active, editor.selection.active);
    cleanCodeDiffDecorations(editor);
    setCurrentShownEdit(undefined, undefined, '');
    updateBlockEditContext(false);
    editManager.acceptOrReject();
    editManager.next();
    editManager.involved = false;
    editManager.showCurrentEdit(false);
}

export function cancelEdit() {
    if (!currentShownEdit.editor || !currentShownEdit.range) {
        return;
    }
    const editor = currentShownEdit.editor;
    cleanCodeDiffDecorations(editor);
    setCurrentShownEdit(undefined, undefined, '');
    updateBlockEditContext(false);
}
