import axios, { AxiosResponse } from 'axios';
import { envManager } from '@/judge_env';

interface Message {
    role: string;
    content: string;
}

interface ChatCompletionRequest {
    messages: Message[];
    chat_template_kwargs?: {
        enable_thinking?: boolean;
    };
    model?: string;
    max_tokens?: number;
}

interface ChatCompletionResponse {
    // 根据实际API响应结构定义
    id?: string;
    object?: string;
    created?: number;
    model?: string;
    choices?: Array<{
        index: number;
        message: {
            role: string;
            content: string;
        };
        finish_reason: string;
    }>;
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}

export async function analyzeCodeChanges(
    codeBefore: string,
    codeAfter: string,
    systemPrompt: string = 'you are a coding assistant.'
): Promise<ChatCompletionResponse> {
    const userContent = `
请分析代码变更并生成简明摘要，包含：

1. 变更类型（新增/删除/修改/无变化）
2. 具体变更内容（若仅涉及空行删除则直接说明）

## 参考示例

### 示例一

变更前代码

\`\`\`typescript
currentShownEdit.editor = editor;
\`\`\`

变更后代码

\`\`\`typescript
cse.editor = editor;
\`\`\`

摘要
修改: 变量名 currentShownEdit -> cse

### 示例二

变更前代码

\`\`\`typescript
currentShownEdit.editor = editor;
\`\`\`

变更后代码

\`\`\`typescript

\`\`\`

摘要
删除: 删除整行代码

### 示例三

变更前代码

\`\`\`typescript

\`\`\`

变更后代码

\`\`\`typescript
\`\`\`

摘要
删除: 删除空行

### 示例四

变更前代码

\`\`\`typescript
currentShownEdit.editor  = editor;
\`\`\`

变更后代码

\`\`\`typescript
currentShownEdit.editor = editor;
\`\`\`

摘要
删除: 调整代码样式，删除空格

### 当前任务

变更前代码
\`\`\`
${codeBefore}
\`\`\`

变更后代码
\`\`\`
${codeAfter}
\`\`\`

输出必须符合以下要求：
- 单句输出，不要换行，突出主要变更
- 注意区分任务重变更前后代码的大小写
- 注意识别代码中不可见字符的改变
- 中文表述，保持简洁
`;

    console.log(userContent);

    const requestData: ChatCompletionRequest = {
        messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userContent }
        ],
        chat_template_kwargs: {
            enable_thinking: false
        },
        model: 'default',
        max_tokens: 1000,
    };

    try {
        const response: AxiosResponse<ChatCompletionResponse> = await axios.post(
            envManager.defaultCopilotApi + '/codefactory/qwen3-4b_no-think/v1/chat/completions',
            requestData,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'X-MODEL-NAME': 'dp-v2lite-ft-vcos-ep-auto'
                }
            }
        );

        return response.data;
    } catch (error) {
        if (axios.isAxiosError(error)) {
            console.error('Request failed:', error.response?.status, error.response?.statusText);
            throw new Error(`API request failed: ${error.message}`);
        } else {
            console.error('Unexpected error:', error);
            throw error;
        }
    }
}

// 使用示例
// async function example() {
//     try {
//         const result = await analyzeCodeChanges(
//             'DEV a ',
//             'DEV a'
//         );
//         console.log(result);

//         // 获取分析结果
//         if (result.choices && result.choices.length > 0) {
//             console.log('', result.choices[0].message.content);
//         }
//     } catch (error) {
//         console.error('Failed to analyze code changes:', error);
//     }
// }

// example();
