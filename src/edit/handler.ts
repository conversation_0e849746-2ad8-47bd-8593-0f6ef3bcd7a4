import { getCopilotConf } from '@/config';
import { agent } from '@/agent';
import { isAuthorized } from '@/menu';
import { CompletionContext } from '@/completion/inline';
import { sleep } from '@/utils';
import { editManager } from '@/edit/editManager';
import * as vscode from 'vscode';
import { appChannel } from '@/extension';
import { CHANNEL_DEV } from '@/config';
import { cancelEdit } from '@/edit/acception';

const SEQ_MAX = 999999;

const DELAY_MILLIS_QUERY_CACHED = 50;

class NextEditProvider {
  private completionCounter: number = 0;
  private latestMillis: number = 0;

  private nextSeq(): number {
    if (this.completionCounter >= SEQ_MAX) {
      this.completionCounter = 0;
    }
    return ++this.completionCounter;
  }
  private timeout: NodeJS.Timer | undefined = undefined;

  public async provideNextEdit(
    event: vscode.TextDocumentChangeEvent
  ) {
    const copilotConf = getCopilotConf();
    const blockEditEnabled = copilotConf.get('nextEdit.enabled', false);
    if ((appChannel() !== CHANNEL_DEV && !blockEditEnabled) || !agent().readyForCompletion || !isAuthorized()) {
      return;
    }
    if (event.contentChanges.length === 0) {
      return;
    }
    const changeText = event.contentChanges[0].text;
    if (editManager.acceptedEdits.includes(changeText)) {
      return;
    }
    cancelEdit();
    editManager.resetEditManager();
    const startedAt = Date.now();
    this.latestMillis = startedAt;

    // Quick match cached
    await sleep(DELAY_MILLIS_QUERY_CACHED);
    if (startedAt < this.latestMillis) {
      return;
    }
    const seq = this.nextSeq();
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }
    const position = editor.selection.active;

    const ctx = new CompletionContext(seq, event.document, position, {} as vscode.InlineCompletionContext, {} as vscode.CancellationToken);

    if (this.timeout) {
      clearTimeout(this.timeout);
    }

    // 设置新的定时器，延迟执行
    this.timeout = setTimeout(() => {
      // 在这里执行你的功能
      console.log('next edit');
      ctx.blockEditRequest();
    }, 1000); // 延迟
    return;
  }
}

export const nextEditProvider = new NextEditProvider();
