import * as vscode from 'vscode';
import { DiffBlock } from './diff';

import {
  decorateCodeDiff,
} from '@/utils/vscode/text-editor-decoration';
import { setCurrentShownEdit } from '@/edit/acception';
import { updateBlockEditContext } from '@/context/setContext';

export interface EditSession {
  documentUri: vscode.Uri;
  editor: vscode.TextEditor | undefined;
  range: vscode.Range;
  edits: DiffBlock[];
  currentIndex: number;
}

export class EditManager {
  private currentSession: EditSession | null = {
    documentUri: vscode.Uri.file(''),
    editor: undefined,
    range: new vscode.Range(0, 0, 0, 0),
    edits: [],
    currentIndex: 0
  };
  private _acceptedEdits: string[];
  public involved: boolean;

  constructor() {
    this.currentSession = {
      documentUri: vscode.Uri.file(''),
      editor: undefined,
      range: new vscode.Range(0, 0, 0, 0),
      edits: [],
      currentIndex: 0
    };
    this._acceptedEdits = [];
    this.involved = false;
  }

  public set acceptedEdits(edits: string[]) {
    this._acceptedEdits.push(...edits);
    if (this._acceptedEdits.length > 10) {
      this._acceptedEdits.shift();
    }
  }

  public get acceptedEdits(): string[] {
    return this._acceptedEdits;
  }

  public resetEditManager() {
    this.currentSession = {
      documentUri: vscode.Uri.file(''),
      editor: undefined,
      range: new vscode.Range(0, 0, 0, 0),
      edits: [],
      currentIndex: 0
    };
    this._acceptedEdits = [];
    this.involved = false;
  }

  // Set edits for the current session
  public set edits(edits: DiffBlock[]) {
    if (this.currentSession) {
      // hardcode
      edits = edits.filter((edit) => {
        if (edit.oldText.trim() === '' && edit.newText.trim() === '') {
          return false;
        }
        if (edit.summary.includes('currentShownEdit')) {
          return false;
        }
        return true;
      });
      console.log(edits, 'diffBlocks');
      this.currentSession.edits = edits;
      this.currentSession.currentIndex = 0;
      const codeRange = this.currentSession.range;
      const activeEditor = this.currentSession.editor;
      if (!activeEditor) {
        return;
      }
      const visibleRanges = activeEditor.visibleRanges;
      for (let idx = 0; idx < edits.length; idx++) {
        const diffBlock = this.currentSession.edits[idx];
        const newCodeRange = this.calcNewCodeRange(diffBlock, codeRange, activeEditor);
        if (visibleRanges.some(visibleRange => visibleRange.contains(newCodeRange))) {
          this.currentSession.currentIndex = idx;
          break;
        }
      }
    }
  }

  public calcOffset() {
    if (!this.currentSession) {
      return;
    }
    const diffBlock = this.currentSession?.edits[this.currentSession.currentIndex];
    const offset = (
      (diffBlock?.newTextEndLine || 0) - (diffBlock?.newTextStartLine || 0) + 1)
      - ((diffBlock?.oldTextEndLine || 0) - (diffBlock?.oldTextStartLine || 0) + 1)
    if (offset === 0) {
      return;
    }
    for (let i = this.currentSession?.currentIndex + 1; i < this.currentSession?.edits.length; i++) {
      this.currentSession.edits[i].offset += offset;
    }
  }

  public set editor(editor: vscode.TextEditor | undefined) {
    if (this.currentSession) {
      this.currentSession.editor = editor;
    }
  }

  public set range(range: vscode.Range) {
    if (this.currentSession) {
      this.currentSession.range = range;
    }
  }

  public get edits(): DiffBlock[] {
    return this.currentSession ? this.currentSession.edits : [];
  }

  private calcNewCodeRange(diffBlock: DiffBlock, codeRange: vscode.Range, activeEditor: vscode.TextEditor) {
    return new vscode.Range(
      new vscode.Position(
        codeRange.start.line + diffBlock.oldTextStartLine + diffBlock.offset,
        diffBlock.oldTextStartLine === 0 ? codeRange.start.character : 0),
      new vscode.Position(
        codeRange.start.line + diffBlock.oldTextEndLine + diffBlock.offset,
        diffBlock.oldTextEndLine === (codeRange.end.line - codeRange.start.line) ? codeRange.end.character : activeEditor.document.lineAt(codeRange.start.line + diffBlock.oldTextEndLine).range.end.character)
    );
  }

  public showCurrentEdit(editable: boolean = true) {
    if (!this.currentSession || this.currentSession.edits.length < 1) {
      return;
    }
    if (!this.currentSession.editor || !this.currentSession.range) {
      return;
    }
    const activeEditor = this.currentSession.editor;
    const codeRange = this.currentSession.range;

    const diffBlock = this.currentSession.edits[this.currentSession.currentIndex];
    const newCodeRange = this.calcNewCodeRange(diffBlock, codeRange, activeEditor);
    if (editable) {
      activeEditor.revealRange(newCodeRange, vscode.TextEditorRevealType.Default);
      updateBlockEditContext(true);
    }
    const constrictOldCode = activeEditor.document.getText(newCodeRange);
    setCurrentShownEdit(activeEditor, newCodeRange, diffBlock.newText);
    decorateCodeDiff(
      activeEditor,
      newCodeRange,
      constrictOldCode,
      diffBlock.newText,
      diffBlock.summary || '');
    if (editable) {
      const startPosition = activeEditor.selection.active.with(newCodeRange.start.line, newCodeRange.start.character);
      const endPosition = activeEditor.selection.active.with(newCodeRange.end.line, newCodeRange.end.character);
      activeEditor.selection = new vscode.Selection(startPosition, endPosition);
    }
  }

  public acceptOrReject() {
    if (!this.currentSession || this.currentSession.edits.length < 1) {
      return;
    }
    editManager.calcOffset();
    const cidx = this.currentSession.currentIndex;
    this.previous();
    this.currentSession.edits = this.currentSession.edits.filter((_, idx) => idx !== cidx);
  }

  public prioritizeFim() {
    if (!this.currentSession || this.currentSession.edits.length < 1) {
      return;
    }
    const cidx = this.currentSession.currentIndex;
    const diffBlock = this.currentSession.edits[cidx];
    const activeEditor = this.currentSession.editor;
    const codeRange = this.currentSession.range;
    if (!activeEditor) {
      return;
    }
    const newCodeRange = this.calcNewCodeRange(diffBlock, codeRange, activeEditor);
    if (activeEditor.selection.active.line === newCodeRange.start.line) {
      this.next();
      this.showCurrentEdit(false);
    }
  }

  public next() {
    if (!this.currentSession || this.currentSession.edits.length < 1) {
      return;
    }
    if (!this.involved) {
      this.involved = true;
      return;
    }
    this.currentSession.currentIndex++;
    if (this.currentSession.currentIndex >= this.currentSession.edits.length) {
      this.currentSession.currentIndex = 0;
    }
  }

  public previous() {
    if (!this.currentSession || this.currentSession.edits.length < 1) {
      return;
    }
    if (!this.involved) {
      this.involved = true;
      return;
    }
    this.currentSession.currentIndex--;
    if (this.currentSession.currentIndex < 0) {
      this.currentSession.currentIndex = this.currentSession.edits.length - 1;
    }
  }
}

export const editManager = new EditManager();
