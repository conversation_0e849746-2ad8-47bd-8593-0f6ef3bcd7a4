import * as vscode from 'vscode';
import { acceptOrRejectEdit, cancelEdit } from './acception';
import { editManager } from './editManager';


export const blockEditAcceptCommand = 'li.codebuddy.nextEdit.accept';
export const blockEditRejectCommand = 'li.codebuddy.nextEdit.reject';
export const blockEditCancelCommand = 'li.codebuddy.nextEdit.cancel';
export const blockEditNextCommand = 'li.codebuddy.nextEdit.background.next';
export const blockEditPreviousCommand = 'li.codebuddy.nextEdit.background.previous';

export async function registerEditCommands(
    _context: vscode.ExtensionContext
): Promise<vscode.Disposable[]> {
    return [
        vscode.commands.registerCommand(blockEditAcceptCommand, () => { acceptOrRejectEdit(true); }),
        vscode.commands.registerCommand(blockEditCancelCommand, cancelEdit),
        vscode.commands.registerCommand(blockEditNextCommand, () => {
            editManager.next();
            editManager.showCurrentEdit();
        }),
        vscode.commands.registerCommand(blockEditPreviousCommand, () => {
            editManager.previous();
            editManager.showCurrentEdit();
        }),
        vscode.commands.registerCommand(blockEditRejectCommand, () => { acceptOrRejectEdit(false); }),
    ];
}
