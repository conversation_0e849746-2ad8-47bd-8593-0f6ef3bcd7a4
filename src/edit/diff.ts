import * as diff from 'diff';

/**
 * 差异类型枚举
 */
export enum DiffType {
    /** 新增内容 */
    added = 'added',
    /** 删除内容 */
    removed = 'removed',
    /** 修改内容 */
    modified = 'modified'
}

/**
 * 差异块接口
 */
export interface DiffBlock {
    /** 旧文本内容 */
    oldText: string;
    /** 新文本内容 */
    newText: string;
    /** 旧文本起始行号（从0开始） */
    oldTextStartLine: number;
    /** 旧文本结束行号（从0开始） */
    oldTextEndLine: number;
    /** 新文本起始行号（从0开始） */
    newTextStartLine: number;
    /** 新文本结束行号（从0开始） */
    newTextEndLine: number;
    /** 差异类型 */
    type?: DiffType;
    summary: string;
    offset: number;
}

/**
 * diff 库的变更项接口
 */
export interface Change {
    /** 是否为新增 */
    added?: boolean;
    /** 是否为删除 */
    removed?: boolean;
    /** 变更的值 */
    value: string;
    /** 行数 */
    count?: number;
}


function splitTextToLines(text: string): string[] {
    if (!text) {
        return [];
    }
    return text.split(/\r?\n/);
}


export function splitDiffBlocksWithModifications(oldText: string, newText: string): DiffBlock[] {
    // 使用 diff 库进行行级别的差异对比
    const changes: Change[] = diff.diffLines(oldText, newText);

    const diffBlocks: DiffBlock[] = [];
    let oldLineIndex = 0;
    let newLineIndex = 0;

    // 临时存储连续的删除和添加操作，用于检测修改
    let pendingRemoved: Change[] = [];
    let pendingAdded: Change[] = [];

    const processPendingChanges = () => {
        if (pendingRemoved.length > 0 && pendingAdded.length > 0) {
            // 有连续的删除和添加，视为修改
            const removedText = pendingRemoved.map(c => c.value).join('').replace(/\n$/, '');
            const addedText = pendingAdded.map(c => c.value).join('').replace(/\n$/, '');
            const removedLineCount = pendingRemoved.reduce((sum, c) => sum + (c?.count || 0), 0);
            const addedLineCount = pendingAdded.reduce((sum, c) => sum + (c?.count || 0), 0);
            diffBlocks.push({
                oldText: removedText,
                newText: addedText,
                oldTextStartLine: oldLineIndex,
                oldTextEndLine: oldLineIndex + removedLineCount - 1,
                newTextStartLine: newLineIndex,
                newTextEndLine: newLineIndex + addedLineCount - 1,
                type: DiffType.modified,
                offset: 0,
                summary: '',
            });
            oldLineIndex += removedLineCount;
            newLineIndex += addedLineCount;
        } else if (pendingRemoved.length > 0) {
            // 只有删除
            for (const change of pendingRemoved) {
                const lineCount = change?.count || 0;
                diffBlocks.push({
                    oldText: change.value.replace(/\n$/, ''),
                    newText: '',
                    oldTextStartLine: oldLineIndex,
                    oldTextEndLine: oldLineIndex + lineCount - 1,
                    newTextStartLine: newLineIndex,
                    newTextEndLine: newLineIndex,
                    type: DiffType.removed,
                    offset: 0,
                    summary: '',
                });
                oldLineIndex += lineCount;
            }
        } else if (pendingAdded.length > 0) {
            // 只有添加
            for (const change of pendingAdded) {
                const lineCount = change?.count || 0;
                diffBlocks.push({
                    oldText: '',
                    newText: change.value.replace(/\n$/, ''),
                    oldTextStartLine: oldLineIndex,
                    oldTextEndLine: oldLineIndex,
                    newTextStartLine: newLineIndex,
                    newTextEndLine: newLineIndex + lineCount - 1,
                    type: DiffType.added,
                    offset: 0,
                    summary: '',
                });
                newLineIndex += lineCount;
            }
        }
        pendingRemoved = [];
        pendingAdded = [];
    };

    for (const change of changes) {
        if (change.added) {
            pendingAdded.push(change);
        } else if (change.removed) {
            pendingRemoved.push(change);
        } else {
            // 处理之前积累的变更
            processPendingChanges();

            // 相同内容，跳过
            oldLineIndex += change.count || 0;
            newLineIndex += change.count || 0;
        }
    }
    // 处理最后的待处理变更
    processPendingChanges();
    return diffBlocks;
}
