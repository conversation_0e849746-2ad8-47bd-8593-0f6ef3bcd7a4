import * as vscode from 'vscode';
import {
  CODE_FACTORY_CMD_EXPLAIN,
  CODE_FACTORY_CMD_FIX,
  CODE_FACTORY_CMD_IMPROVE,
} from '@/commands';
import { telemetry } from '@/telemetry';
import {
  getVscodeRange,
} from '@/testing/utils';


interface OptionItem extends vscode.QuickPickItem {
  cmd?: string;
}

const options: OptionItem[] = [
  {
    label: '解释代码',
    cmd: CODE_FACTORY_CMD_EXPLAIN,
  },
  {
    label: '修复代码',
    cmd: CODE_FACTORY_CMD_FIX,
  },
  {
    label: '代码重构',
    cmd: CODE_FACTORY_CMD_IMPROVE,
  }
];

export async function showOptions(obj: any) {
  const picked = await vscode.window.showQuickPick(options, {
    canPickMany: false
  });

  const cmd = picked?.cmd;
  if (!cmd) {
    return;
  }

  telemetry().offer({
    name: 'view:codelen.options.picked',
    details: {
      label: picked.label,
      cmd
    }
  });
  const editor = vscode.window.activeTextEditor;
  if (editor) {
    const range = getVscodeRange(obj);
    if (range) {
      const selection = new vscode.Selection(
        range.start, range.end,
      );
      editor.selection = selection;
      editor.revealRange(selection);
    }
  }
  vscode.commands.executeCommand(cmd, obj, { 'codebuddy': true });
}
