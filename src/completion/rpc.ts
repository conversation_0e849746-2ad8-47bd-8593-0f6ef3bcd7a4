import { BaseWebviewRpc } from '@/utils/webview';
import { Choice } from '@/agent';

export interface SuggestionItem {
  index: number;
  text: string;
  model?: string;
}

export interface SuggestionsCompletion {
  id: string;
  requestId: string;
  prefix: string;
  suffix: string;
  language: string;
  choices: Choice[];
}

export interface SuggestionAcceptForm {
  id: string;
  requestId: string;
  choice: Choice;
}

export class SuggestionsPanelRpc extends BaseWebviewRpc {

  readonly suggestions = {
    show: (form: SuggestionsCompletion): Promise<void> => {
      return this._req('suggestions/show', form);
    },
    processing: (): Promise<void> => {
      return this._req('suggestions/processing');
    },
  };

}
