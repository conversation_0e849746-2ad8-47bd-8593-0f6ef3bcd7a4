import vscode, { commands, Position, TextEditor, Uri } from 'vscode';
import { CMD, DEFAULT_SUGGUESTION_PANEL_PAGE_URL } from '@/config';
import {
  SuggestionAcceptForm,
  SuggestionsCompletion,
  SuggestionsPanelRpc
} from '@/completion/rpc';
import { downloadToString } from '@/utils/download';
import { RpcProtocol } from '@/utils/rpc';
import { agent } from '@/agent';
import { randomUUID } from 'crypto';
import { BaseWebviewPanel, webviewPanelToContainer } from '@/utils/webview';
import { isAuthorized } from '@/menu';
import { notify } from '@/notify';
import { consoleDebug } from '@/extension';
import { telemetry } from '@/telemetry';

const CMD_SUGGUEST = `${CMD}.suggestions`;
const CMD_SUGGUEST_GENERATE = `${CMD_SUGGUEST}.generate`;

let PANEL: SuggestionsPanel | undefined;

function panel(): SuggestionsPanel {
  if (PANEL) {
    return PANEL;
  }

  const webview = vscode.window.createWebviewPanel(
    'SuggestionsPanel',
    'CodeBuddy Suggestions',
    vscode.ViewColumn.Two,
    { enableScripts: true }
  );
  webview.iconPath = Uri.file('assets/icons/suggestions.svg');
  webview.onDidDispose(() => PANEL = undefined);

  PANEL = new SuggestionsPanel(webview);
  return PANEL;
}

function dispose() {
  if (PANEL) {
    PANEL.dispose();
  }
}

export async function initSuggestionsPanel(
  _context: vscode.ExtensionContext
): Promise<vscode.Disposable[]> {

  return [
    commands.registerCommand(CMD_SUGGUEST_GENERATE, async () => {
      await panel().generate();
    }),
    { dispose }
  ];
}

function insertText(
  editor: TextEditor | undefined,
  text: string,
  pos?: Position,
) {
  if (!editor) {
    console.log('ERROR: No active editor to insert text');
    return;
  }

  const target = pos || editor.selection.active;
  editor.edit(editBuilder => {
    editBuilder.insert(target, text);
  }).then(success => {
    if (!success) {
      console.log('ERROR: Insert text failed!');
    }
  });
}

class SuggestionsPanel extends BaseWebviewPanel<SuggestionsPanelRpc> {

  latestEditor?: TextEditor;

  constructor(webviewPanel: vscode.WebviewPanel) {
    super();
    this._resetWebview(webviewPanelToContainer(webviewPanel));
  }

  protected async _active() {
    this.show();
  }

  protected async _resolvePageHtml() {
    const url = DEFAULT_SUGGUESTION_PANEL_PAGE_URL;
    const html = await downloadToString(url);
    return html;
  }

  protected async _createApi(protocol: RpcProtocol) {
    return new SuggestionsPanelRpc(protocol);
  }

  protected async _setup() {
    const api = this.rpc;

    const sub = this.subscribeReady(async (ready) => {
      if (ready) {
        sub.unsubscribe();
        const metrics = this.setupMetrics;
        if (!metrics) {
          return;
        }
        const now = Date.now();
        const { startTime, finishedTime, setupWebviewCost } = metrics;
        telemetry().offer({
          name: 'view:suggestions-panel.ready',
          time: startTime,
          du: now - startTime,
          details: {
            setupCost: finishedTime - startTime,
            setupWebviewCost,
          }
        });
      }
    });

    api.register('suggestions/accept', async (form: SuggestionAcceptForm) => {
      consoleDebug('Accept suggestion: ', form);
      const editor = vscode.window.activeTextEditor || this.latestEditor;
      insertText(editor, form.choice.text);

      agent().emitCompletionAccepted({
        id: form.id,
        index: form.choice.index,
      });
      return true;
    });
  }

  private async _generateForm(editor: TextEditor, position: Position) {
    const doc = editor.document;
    const language = doc.languageId;

    const cancellationTokenSource = new vscode.CancellationTokenSource();

    const requestId = randomUUID();
    const completion = await agent().resolveCompletion(
      cancellationTokenSource.token, {
        requestId,
        doc,
        position,
        scene: 'panel',
        choiceLimit: 4,
      }
    );

    const currentLine = doc.lineAt(position.line).text;
    const prefix = currentLine.substring(0, position.character);
    const suffix = currentLine.substring(position.character);

    const form: SuggestionsCompletion = {
      id: completion.id,
      requestId: requestId,
      prefix,
      suffix,
      language: language,
      choices: completion.choices
        .filter((c) => c.text !== ''),
    };
    return form;
  }

  public async generate() {
    if (!isAuthorized()) {
      notify.requireLogin();
      return;
    }

    let editor = vscode.window.activeTextEditor;
    if (!editor) {
      console.log('WARN: No active editor to show suggestions');
      return;
    }
    this.latestEditor = editor;
    const position = editor.selection.active;

    this.show();
    const [_, form] = await Promise.all([
      this.readyAndRun(async () =>
        await this.rpc.suggestions.processing()
      ),
      this._generateForm(editor!, position)
    ]);

    // console.log('Show suggestions: ', form);
    const responsedAt = Date.now();
    await this.rpc.suggestions.show(form);
    agent().emitCompletionEvent({
      id: form.id,
      requestId: form.requestId,
      code: 'shown',
      time: responsedAt,
      du: Date.now() - responsedAt
    });
  }

}
