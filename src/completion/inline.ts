import * as vscode from 'vscode';

import {
  CancellationToken,
  commands,
  Disposable,
  ExtensionContext,
  InlineCompletionContext,
  InlineCompletionItem,
  InlineCompletionItemProvider,
  languages,
  Position,
  Range,
  TextDocument
} from 'vscode';
import { countBlankPrefix, isBlank, sleep, splitLines } from '@/utils';
import { CMD, getCopilotConf, onConfigChanged, PLUGIN_LABEL } from '@/config';
import { agent, Choice, CompletionResponse } from '@/agent';
import { addGeneratedRanges } from '@/tracker/doc-tracker';
import { onAccepted, onCompleting } from '@/tracker/index';
import { CompletionShownTracker } from '@/tracker/completion-shown-tracker';
import { randomUUID } from 'crypto';
import { isAuthorized } from '@/menu';
import { consoleDebug } from '@/extension';
import { UserWrittenCodeTracker } from '@/tracker/user-written-code-tracker';
import {
  cleanCodeDiffDecorations,
} from '@/utils/vscode/text-editor-decoration';
import { setCurrentShownEdit } from '@/edit/acception';
import { splitDiffBlocksWithModifications } from '@/edit/diff';
import { editManager } from '@/edit/editManager';
import { analyzeCodeChanges } from '@/edit/diffSummary';

const CMD_ON_INLINE_ACCEPTED = `${CMD}.copilot.inline-completion.accepted`;
const COMPLETION_SHOWN_TRACKER = new CompletionShownTracker(750);

export async function initInlineCompletion(
  _context: ExtensionContext
): Promise<Disposable[]> {
  const provider = new CompletionProviderImpl();
  provider.updateConfig();
  return [
    commands.registerCommand(
      CMD_ON_INLINE_ACCEPTED,
      onInlineCompletionAccepted
    ),
    onConfigChanged(_ => {
      provider.updateConfig();
    }),
    languages.registerInlineCompletionItemProvider(
      // TODO 限定语言
      { pattern: '**' },
      provider
    )
  ];
}

export interface AcceptedEvent {
  doc: TextDocument;
  response: CompletionResponse;
  choice: Choice;
  range: Range;
}

const SEQ_MAX = 999999;

const DELAY_MILLIS_QUERY_CACHED = 50;

const DELAY_MILLIS_BASELINE = 150;
const DELAY_MILLIS_STEP = 200;
const DELAY_MILLIS_MAX = 1400;

let delayContext: DelayContext = defaultDelayContext(0);

interface DelayContext {
  time: number,
  uri: string,
  latencyMillis: number,
  suggested: number,
}

function onInlineCompletionAccepted(event: AcceptedEvent) {
  if (!event.response || !event.choice) {
    return;
  }
  resetDelayContext(0);

  onAccepted(event);
  COMPLETION_SHOWN_TRACKER.emitAccepted(event.response.id);
  const doc = event.doc;
  const choice = event.choice;
  const response = event.response;

  agent().emitCompletionAccepted({
    id: response.id,
    index: choice.index
  });

  const ranges = resolveRanges(choice.text, event.range);
  addGeneratedRanges(doc, ranges);
}

function resolveRanges(text: string, range: Range): Range[] {
  const lines = splitLines(text);
  if (lines.length === 0) {
    return [];
  }
  const ranges: Range[] = [];

  let startLine = range.start.line;
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const blankPrefix = countBlankPrefix(line);
    if (blankPrefix === line.length) {
      continue;
    }

    let lineNum = startLine + i;
    let columnNum = i === 0 ? range.start.character : 0;
    columnNum += blankPrefix;

    const start = new Position(lineNum, columnNum);
    const end = new Position(lineNum, columnNum + line.length);
    ranges.push(new Range(start, end));
  }
  return ranges;
}

function defaultDelayContext(time: number): DelayContext {
  return {
    time,
    uri: '',
    latencyMillis: 0,
    suggested: 0,
  };
}

export function resetDelayContext(time: number) {
  delayContext = defaultDelayContext(time);
}

export function getDelayMillis(baseline: number, uri: string): number {
  const now = Date.now();

  if ((now - delayContext.time) >= 5 * 60 * 1000 || delayContext.uri !== uri) {
    resetDelayContext(now);
  }
  delayContext.uri = uri;

  const total = Math.max(
    baseline,
    Math.min(baseline + delayContext.latencyMillis, DELAY_MILLIS_MAX)
  );

  if (delayContext.suggested >= 5 && delayContext.latencyMillis < DELAY_MILLIS_MAX) {
    delayContext.latencyMillis += DELAY_MILLIS_STEP;
  }
  return total;
}

class CompletionProviderImpl implements InlineCompletionItemProvider {

  // User Settings
  private enabled: boolean = true;
  private delayMsBaseline: number = DELAY_MILLIS_BASELINE;

  private completionCounter: number = 0;
  private latestMillis: number = 0;

  private nextSeq(): number {
    if (this.completionCounter >= SEQ_MAX) {
      this.completionCounter = 0;
    }
    return ++this.completionCounter;
  }

  public async provideInlineCompletionItems(
    doc: TextDocument,
    position: Position,
    context: InlineCompletionContext,
    token: CancellationToken
  ): Promise<InlineCompletionItem[]> {
    COMPLETION_SHOWN_TRACKER.emitAll();
    if (!this.enabled
      || !agent().readyForCompletion
      || !isAuthorized()) {
      return [];
    }

    if (context.selectedCompletionInfo !== undefined) {
      // TODO: TBD: Do not show completion when there is already a selected completion.
      return [];
    }

    const startedAt = Date.now();
    this.latestMillis = startedAt;

    // Quick match cached
    await sleep(DELAY_MILLIS_QUERY_CACHED);
    if (startedAt < this.latestMillis) {
      return [];
    }
    const seq = this.nextSeq();

    const ctx = new CompletionContext(seq, doc, position, context, token);
    const cached = await ctx.cached();
    if (cached.length > 0) {
      return cached;
    }

    // Query
    ctx.delayMillis = getDelayMillis(this.delayMsBaseline, doc.uri.toString());
    await sleep(ctx.delayMillis - DELAY_MILLIS_QUERY_CACHED);
    if (startedAt < this.latestMillis) {
      return [];
    }
    const result = await ctx.request();
    if (startedAt < this.latestMillis) {
      ctx.onOutdated();
      return [];
    }
    UserWrittenCodeTracker.instance.inlinePendingCompletionTexts = result?.map((item, _) => item.insertText) as string[];
    // 阻止展示 next edit 提示
    editManager.prioritizeFim();
    return result;
  }

  updateConfig() {
    const copilotConf = getCopilotConf();
    this.enabled = copilotConf.get('enabled', true);
    this.delayMsBaseline = copilotConf.get('advance.suggestionDelay', DELAY_MILLIS_BASELINE);
  }

}

export class CompletionContext {

  private readonly seq: number;
  private readonly doc: TextDocument;
  private readonly position: Position;
  private readonly context: InlineCompletionContext;
  private readonly token: CancellationToken;

  // https://code.visualstudio.com/docs/languages/identifiers
  private readonly language: string;
  private readonly requestId: string;

  private requestAt: number;

  delayMillis: number = 0;

  constructor(
    seq: number,
    doc: TextDocument,
    position: Position,
    context: InlineCompletionContext,
    token: CancellationToken
  ) {
    this.seq = seq;
    this.doc = doc;
    this.position = position;
    this.context = context;
    this.token = token;

    this.requestAt = Date.now();

    this.language = doc.languageId;
    this.requestId = randomUUID();
  }

  async cached(): Promise<InlineCompletionItem[]> {
    this.requestAt = Date.now();
    const cached = await agent().listCachedCompletions(this.token, {
      doc: this.doc,
      position: this.position,
      scene: 'inline',
    }
    );

    if (!cached || cached.length === 0) {
      return [];
    }

    consoleDebug(`Got cached completion: No.${this.seq}`, cached);
    // XXX: Only accept first cached for now.
    return await this.processResponse(cached[0]);
  }

  async blockEditRequest(): Promise<InlineCompletionItem[]> {
    this.requestAt = Date.now();
    consoleDebug(
      `[${PLUGIN_LABEL}] start edit completion: No.${this.seq}`
    );
    const activeEditor = vscode.window.activeTextEditor;
    if (!activeEditor) {
      return [];
    }
    const visibleRanges = activeEditor.visibleRanges;
    const pending = agent().predictCompletion({
      requestId: this.requestId,
      doc: this.doc,
      position: this.position,
      scene: 'predict',
      visibleWindow: { start: visibleRanges[0].start.line, end: visibleRanges[0].end.line }
    });

    const response = await pending.catch((error: Error) => {
      consoleDebug(
        `[${PLUGIN_LABEL}] failed to fetch completion: No.${this.seq}`,
        error
      );
      return {
        id: 'error',
        requestId: this.requestId,
        error: error.message,
        choices: []
      } as CompletionResponse;
    });
    onCompleting(this.doc, this.position, response.requestId);
    if (response.error) {
      return Promise.resolve([]);
    }
    const currentPosition = activeEditor?.selection.active;
    const editRange = response?.editableRange;
    if (!editRange) {
      return [];
    }
    const codeRange = new vscode.Range(
      new vscode.Position(editRange.start.line, editRange.start.character),
      new vscode.Position(editRange.end.line, editRange.end.character)
    );
    if (!activeEditor || !currentPosition || !codeRange.contains(currentPosition)) {
      return [];
    }

    const oldCode = activeEditor.document.getText(codeRange);
    const newCode = response.outputExcerpt || '';
    if (oldCode.trim() === newCode.trim()) {
      cleanCodeDiffDecorations(activeEditor);
      return [];
    }
    console.log(`[${PLUGIN_LABEL}] diffBlocks, oldCode:`, oldCode);
    console.log(`[${PLUGIN_LABEL}] diffBlocks, newCode:`, newCode);
    const diffBlocks = splitDiffBlocksWithModifications(oldCode, newCode);
    // 抑制 改动大于 10 行
    const copilotConf = getCopilotConf();
    const enableMultiLinesChange = copilotConf.get('nextEdit.enableMultiLinesChange', false);
    if (!enableMultiLinesChange) {
      for (const edit of diffBlocks) {
        if ((edit.oldTextEndLine - edit.oldTextStartLine) >= 10) {
          return [];
        }
      }
    }
    // 生成 summary
    for (const edit of diffBlocks) {
      const result = await analyzeCodeChanges(edit.oldText, edit.newText);
      if (result.choices && result.choices.length > 0) {
        edit.summary = result.choices[0].message.content;
      }
    }
    console.log(`[${PLUGIN_LABEL}] diffBlocks:`, diffBlocks);
    if (diffBlocks.length === 0) {
      return [];
    }

    editManager.editor = activeEditor;
    editManager.range = codeRange;
    editManager.edits = diffBlocks;
    editManager.showCurrentEdit(false);
    console.log(`[${PLUGIN_LABEL}] editManager.edits:`, editManager.edits);
    return [];
  }

  async request(): Promise<InlineCompletionItem[]> {
    this.requestAt = Date.now();
    consoleDebug(
      `[${PLUGIN_LABEL}] start completion: No.${this.seq} `
    );

    if (this.token.isCancellationRequested) {
      consoleDebug(
        `[${PLUGIN_LABEL}] completion is cancelled, before request: No.${this.seq} `
      );
      return [];
    }

    const pending = agent().resolveCompletion(this.token, {
      requestId: this.requestId,
      doc: this.doc,
      position: this.position,
      scene: 'inline',
    }
    );

    const response = await pending.catch((error: Error) => {
      consoleDebug(
        `[${PLUGIN_LABEL}] failed to fetch completion: No.${this.seq} `,
        error
      );
      return {
        id: 'error',
        requestId: this.requestId,
        error: error.message,
        choices: []
      } as CompletionResponse;
    });

    if (response.error) {
      this.onError(response.error);
      delayContext.suggested++;
      return Promise.resolve([]);
    }

    return await this.processResponse(response);
  }

  private onError(err: string) {
    const costMillis = Date.now() - this.requestAt;
    const code =
      err === 'Request aborted' || err === 'Canceled'
        ? 'aborted'
        : 'failed';

    agent().emitCompletionEvent({
      requestId: this.requestId,
      code,
      du: costMillis,
      time: this.requestAt,
      details: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        request_delay_millis: this.delayMillis,
        lang: this.language,
        error: err,
        requestId: this.requestId
      }
    });
  }

  onOutdated() {
    const costMillis = Date.now() - this.requestAt;
    consoleDebug('completion is outdated');
    agent().emitCompletionEvent({
      requestId: this.requestId,
      code: 'outdated',
      du: costMillis,
      time: this.requestAt,
      details: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        request_delay_millis: this.delayMillis,
        lang: this.language,
      },
    });
  }

  private async processResponse(response: CompletionResponse): Promise<InlineCompletionItem[]> {
    const costMillis = Date.now() - this.requestAt;
    consoleDebug(
      `[${PLUGIN_LABEL}] finished completion: ` +
      `No.${this.seq}, cost ${costMillis} ms`
    );

    response.choices = response.choices || [];
    if (response.choices.length === 0 || response.choices[0].text === '') {
      consoleDebug(`[${PLUGIN_LABEL}] empty completion: ` +
        `No.${this.seq}, cost ${costMillis} ms`);
      return [];
    }

    response.choices = response.choices.filter(choice => {
      return choice.text && !isBlank(choice.text);
    });
    this.fixBySelected(response);

    if (response.choices.length === 0) {
      consoleDebug('completion mismatch-popup');
      agent().emitCompletionEvent({
        requestId: response.requestId,
        code: 'mismatch-popup',
        du: costMillis,
        time: this.requestAt,
        details: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          request_delay_millis: this.delayMillis,
          lang: this.language,
        },
      });
      return [];
    }

    const firstChoice = response.choices[0];
    if (!firstChoice) {
      // Note: DONT inc delayContext.suggested for empty responses
      return [];
    }

    COMPLETION_SHOWN_TRACKER.emitAll();
    COMPLETION_SHOWN_TRACKER.take({
      response,
      requestDelayMillis: this.delayMillis,
    });
    onCompleting(this.doc, this.position, response.id);

    delayContext.suggested++;
    return this.transform(response);
  }

  private transform(resp: CompletionResponse): InlineCompletionItem[] {
    const range = this.context.selectedCompletionInfo?.range
      || new Range(this.position, this.position);

    return resp.choices
      .map(choice => {
        const itemArgs = {
          title: '',
          command: CMD_ON_INLINE_ACCEPTED,
          arguments: [
            {
              doc: this.doc,
              range,
              choice,
              response: resp
            } as AcceptedEvent
          ]
        };
        return new InlineCompletionItem(choice.text, range, itemArgs);
      });
  }

  private fixBySelected(resp: CompletionResponse) {
    const { selectedCompletionInfo } = this.context;
    if (!selectedCompletionInfo) {
      return;
    }
    const { range, text } = selectedCompletionInfo;
    const inputtedText = this.doc.getText(range);

    resp.choices = resp.choices
      .map(choice => {
        choice.text = inputtedText + choice.text;
        return choice;
      })
      .filter(choice => {
        return choice.text.startsWith(text);
      });
  }
}
