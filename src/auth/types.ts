import { interval } from 'rxjs';
import * as vscode from 'vscode';

export interface AuthResult {
    idToken: string,
    accessToken: string,
    refreshToken?: string,
    expiresAt?: number,
    account: Account,
}

export interface Account extends vscode.AuthenticationSessionAccountInformation {
    openid: string,
    nickname: string,
    email: string
}
export interface IDaaSTokenResponse {
    expires_in: number
    id_token: string
    access_token: string
    refresh_token?: string
}

export interface auxResponse {
    device_code: string,
    user_code: string,
    verification_uri: string,
    verification_uri_complete: string,
    verification_uri_overridden: string,
    expires_in: number,
    interval: number,
}