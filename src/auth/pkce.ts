// PKCE 工具类（浏览器 + Node.js 兼容）
export class PKCETool {
    private static readonly CODE_VERIFIER_CHARSET = 
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  
    /**
     * 生成符合 PKCE 规范的 code_verifier
     * @param length 长度（默认 32 字节，即 43 字符）
     */
    static generateVerifier(length = 32): string {
      const bytes = new Uint8Array(length);
      
      if (typeof crypto !== 'undefined' && 'getRandomValues' in crypto) {
        crypto.getRandomValues(bytes);
      } else {
        try {
          const nodeCrypto = require('node:crypto');
          nodeCrypto.randomFillSync(bytes);
        } catch (error) {
          throw new Error('无法生成安全随机数: 请确保在浏览器或 Node.js 环境中运行');
        }
      }
  
      let verifier = '';
      for (const byte of bytes) {
        verifier += this.CODE_VERIFIER_CHARSET[byte % this.CODE_VERIFIER_CHARSET.length];
      }
      return verifier;
    }
  
    /**
     * 生成 code_challenge (强制使用 SHA-256)
     * @param verifier code_verifier
     */
    static async generateChallenge(verifier: string): Promise<string> {
      let digest: ArrayBuffer;
  
      // 跨平台 SHA-256 哈希
      if (typeof crypto !== 'undefined' && 'subtle' in crypto) {
        // 浏览器
        const encoder = new TextEncoder();
        const data = encoder.encode(verifier);
        digest = await crypto.subtle.digest('SHA-256', data);
      } else {
        // Node.js
        try {
          const nodeCrypto = require('node:crypto');
          const hash = nodeCrypto.createHash('sha256');
          hash.update(verifier, 'utf8');
          digest = hash.digest().buffer;
        } catch (error) {
          throw new Error('无法生成哈希: 请检查 Node.js 环境');
        }
      }
  
      // Base64URL 编码（无填充）
      return this.base64UrlEncode(new Uint8Array(digest));
    }
  
    /**
     * Base64URL 编码（无填充）
     * @param bytes 二进制数据
     */
    private static base64UrlEncode(bytes: Uint8Array): string {
      let base64: string;
  
      if (typeof Buffer !== 'undefined') {
        // Node.js
        base64 = Buffer.from(bytes).toString('base64');
      } else {
        // 浏览器
        const binary = Array.from(bytes, (byte) => 
          String.fromCharCode(byte)).join('');
        base64 = btoa(binary);
      }
  
      return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');
    }
  }