import { authRefreshToken, aux, CLIENT_ID, codeChallengeForToken, deviceCodeChangeToken, EP_SERVICE_ID, getUserInfo } from '@/api/auth';
import { notify } from '@/notify';
import * as vscode from 'vscode';
import { Account, AuthResult, IDaaSTokenResponse } from './types';
import { randomUUID } from 'crypto';
import { AuthHandler } from '@/handler/authHandler';
import { UriHandlerImpl } from '@/handler/uriHandler';
import { PKCETool } from './pkce';
import { agent } from '@/agent';
import { authProvider } from '@/extension';
import { localStore } from '@/store';
import { envManager, isRedZone } from '@/judge_env';
import * as qrcode from 'qrcode';


const AUTH_PROVIDER_ID = 'CodeBuddy';
const AUTH_PROVIDER_NAME = 'CodeBuddy_IDaaS';
const REFRESH_TOKEN_THRESHOLD = 60 * 1000 * 10;

class IDaaSSession implements vscode.AuthenticationSession {
    constructor(
        public readonly id: string,
        public readonly accessToken: string,
        public readonly account: Account,
        public readonly idToken: string,
        public readonly expiresAt?: number,
        public readonly refreshToken?: string,
        public readonly scopes: string[] = [],
    ) { }
}
export class IDaasAuthProvider implements vscode.AuthenticationProvider, vscode.Disposable {
    private initializedDisposable: vscode.Disposable | undefined;
    private _onDidChangeSessions: vscode.EventEmitter<vscode.AuthenticationProviderAuthenticationSessionsChangeEvent>;
    private stateId = '';
    private redirectUrl = '';
    private codeVerifier = '';
    private codeChallenge = '';

    private _authHandler: AuthHandler;
    private currentResolve: ((value: AuthResult) => void) | null = null;
    private currentReject: ((reason?: any) => void) | null = null;

    constructor(
        private readonly context: vscode.ExtensionContext,
        private uriHandler: UriHandlerImpl
    ) {
        this._onDidChangeSessions = new vscode.EventEmitter<vscode.AuthenticationProviderAuthenticationSessionsChangeEvent>();
        this.initializedDisposable = vscode.Disposable.from(
            vscode.authentication.registerAuthenticationProvider(
                AUTH_PROVIDER_ID,
                AUTH_PROVIDER_NAME,
                this,
                { supportsMultipleAccounts: false }
            ),
        );

        this.redirectUrl = this.getRedirectUrl;

        console.log(`[CodeBuddy] callback uri: ${this.redirectUrl}`);

        this._authHandler = new AuthHandler(context);
        this._authHandler.bindAuthProvider(this);
        uriHandler.registerHandler(
            '/auth/login/callback',
            this._authHandler,
        );
    }

    dispose() {
        this.initializedDisposable?.dispose();
    }

    get onDidChangeSessions(): vscode.Event<vscode.AuthenticationProviderAuthenticationSessionsChangeEvent> {
        return this._onDidChangeSessions.event;
    }

    get getRedirectUrl(): string {
        const publisher = this.context.extension.packageJSON.publisher;
        const name = this.context.extension.packageJSON.name;
        return `${vscode.env.uriScheme}://${publisher}.${name}/auth/login/callback`;
    }

    async getSessions(): Promise<readonly vscode.AuthenticationSession[]> {
        await localStore.load();
        const sessions = localStore.data.codeBuddySessions;
        if (sessions.length > 0) {
            const { expiresAt, refreshToken } = sessions[0];
            if (expiresAt - REFRESH_TOKEN_THRESHOLD <= Date.now()) {
                try {
                    const iDaasToken = await authRefreshToken(refreshToken);
                    if (!iDaasToken) {
                        return [];
                    }
                    sessions[0].accessToken = iDaasToken.access_token;
                    sessions[0].refreshToken = iDaasToken.refresh_token;
                    sessions[0].idToken = iDaasToken.id_token;
                    sessions[0].expiresAt = Date.now() + iDaasToken.expires_in * 1000;
                    localStore.data.codeBuddySessions = sessions;
                    await localStore.save();
                    this._onDidChangeSessions.fire({ added: [], removed: [], changed: sessions });
                } catch (e) {
                    console.log(`[CodeBuddy]: failed to refresh token ${e}`);
                    return sessions;
                }
            }
            return sessions;
        }
        return [];
    }

    async createSession(): Promise<vscode.AuthenticationSession> {
        try {
            let authResult;
            if (isRedZone()) {
                authResult = await this.deviceAuth();
            }else {
                authResult = await this.auth();
            }

            if (!authResult) {
                throw new Error('debiveAuth: authResult is undefined');
            }
            if (!authResult.accessToken) {
                throw new Error('cannot get accessToken');
            }

            const session: IDaaSSession = {
                id: randomUUID(),
                ...authResult,
                scopes: []
            };
            await localStore.load();
            localStore.data.codeBuddySessions = [session];
            await localStore.save();
            this._onDidChangeSessions.fire({ added: [session], removed: [], changed: [] });
            notify.loginSuccess(authResult.account.nickname, authResult.account.email);
            return session;
        } catch (e) {
            throw e;
        }
    }

    private async auth() {
        return vscode.window.withProgress<AuthResult>({
            location: vscode.ProgressLocation.Notification,
            title: 'CodeBuddy: 正在登录...',
            cancellable: true
        }, async (_, token) => {
            this.stateId = randomUUID();
            this.codeVerifier = PKCETool.generateVerifier();
            this.codeChallenge = await PKCETool.generateChallenge(this.codeVerifier);
            const params = new URLSearchParams([
                ['response_type', 'code'],
                ['client_id', CLIENT_ID],
                ['code_challenge', this.codeChallenge],
                ['code_challenge_method', 'S256'],
                ['redirect_uri', this.redirectUrl],
                ['state', this.stateId],
                ['scope', 'api:all openid email profile offline_access'],
                ['prompt', 'login'],
                ['audience', EP_SERVICE_ID],
            ]);

            const uri = vscode.Uri.parse(`${envManager.idaasBasePath}/auth?${params.toString()}`);
            await vscode.env.openExternal(uri);

            try {
                return new Promise<AuthResult>((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        console.log('[CodeBuddy] auth timeout');
                        this.clearAuthState();
                        reject(new Error('[CodeBuddy] 登录超时'));
                    }, 300000);

                    this.currentResolve = (value) => {
                        clearTimeout(timeout);
                        resolve(value);
                    };
                    this.currentReject = (value) => {
                        clearTimeout(timeout);
                        reject(value);
                    };

                    token.onCancellationRequested(() => {
                        console.log('[CodeBuddy] auth cancelled');
                        clearTimeout(timeout);
                        this.clearAuthState();
                        reject(new vscode.CancellationError());
                    });

                });
            } catch (e) {
                console.log(`[CodeBuddy] login failed: ${JSON.stringify(e)}`);
                throw e;
            }
        });
    }

    private async deviceAuth(): Promise<AuthResult | undefined> {
        const device = await aux();
        const url = device.verification_uri_complete || device.verification_uri;

        if (!device || !device.device_code || !url) {
            throw new Error('device aux error');
        }

        const panel = await showQrWebviewPanel(url);

        const expiresMs = device.expires_in * 1000;
        let intervalMs = (device.interval || 5) * 1000;
        const start = Date.now();

        while (Date.now() - start < expiresMs) {
            try {
                const tokens = await deviceCodeChangeToken(device.device_code);
                const account = await this.getUserInfo(tokens.access_token, tokens.id_token);

                panel.dispose();

                const authResult: AuthResult = {
                    idToken: tokens.id_token,
                    accessToken: tokens.access_token,
                    refreshToken: tokens.refresh_token,
                    expiresAt: tokens.expires_in * 1000 + Date.now(),
                    account,
                };
                return authResult;
            } catch (e: any) {
                if (e.response && e.response.data) {
                    const err = e.response.data.error;
                    if (err === 'authorization_pending') {
                        // continue
                    } else if (err === 'slow_down') {
                        intervalMs += 5000;
                    } else if (err === 'access_denied' || err === 'access_insufficient') {
                        console.log('assistant auth rejected, or the user has insufficient authorization for assistant authentication');
                        throw e;
                    } else {
                        console.log('other: device auth session timed out');
                        throw e;
                    }
                } else {
                    console.log('bad device auth request: ', e);
                    throw e;
                }
            }
            await new Promise((r) => setTimeout(r, intervalMs));
        }

        throw new Error('device auth timeout');
    }

    public resolveAuth(authResult: AuthResult) {
        this.currentResolve?.(authResult);
        this.clearAuthState();
    }

    public rejectAuth(error: Error) {
        this.currentReject?.(error);
        this.clearAuthState();
    }

    private clearAuthState() {
        this.currentResolve = null;
        this.currentReject = null;
        this.stateId = '';
    }

    validateState(returnState: string): boolean {
        return this.stateId === returnState;
    }

    async codeChallengeForToken(code: string): Promise<IDaaSTokenResponse> {
        return await codeChallengeForToken(code, this.codeVerifier);
    }

    async getUserInfo(accessToken: string, idToken: string): Promise<Account> {
        return await getUserInfo(accessToken, idToken);
    }

    async removeSession(): Promise<void> {
        await localStore.load();
        const sessions = localStore.data.codeBuddySessions;
        localStore.data.codeBuddySessions = [];
        await localStore.save();

        if (sessions.length > 0) {
            this._onDidChangeSessions.fire({
                added: [],
                removed: sessions,
                changed: []
            });
        }
    }
}

export async function authLogin() {
    try {
        const session = await vscode.authentication.getSession(AUTH_PROVIDER_ID, [], { createIfNone: true }) as unknown as IDaaSSession | undefined;
        if (!session || !session.account) {
            throw new Error('cannot get session or session account');
        }

        agent().emit_auth({
            authorized: true,
            account: {
                openid: session.account.openid,
                nickname: session.account.nickname,
                email: session.account.email,
            }
        });
        // notify agent
        await agent().account({
            openid: session.account.openid,
            email: session.account.email,
            nickname: session.account.nickname
        });
    } catch (e) {
        console.log(`[CodeBuddy] 登录失败: ${JSON.stringify(e)}`);
        agent().emit_auth({ authorized: false });
        notify.whenLoginFailed(JSON.stringify(e));
    }
}

export async function logout() {
    await authProvider.removeSession();
    agent().emit_auth({ authorized: false });
    await agent().logout();
}

export async function getAccount(): Promise<Account | undefined> {
    try {
        const session = await vscode.authentication.getSession(AUTH_PROVIDER_ID, [], { createIfNone: false }) as unknown as IDaaSSession | undefined;
        if (!session) {
            throw new Error('getAccount failed, cannot get session');
        }
        return session.account;
    } catch (e) {
        return undefined;
    }
}

export async function getAccessToken(): Promise<String | undefined> {
    try {
        const session = await vscode.authentication.getSession(AUTH_PROVIDER_ID, [], { createIfNone: false }) as unknown as IDaaSSession | undefined;
        if (!session || !session.accessToken || !session.expiresAt || session.expiresAt <= Date.now()) {
            throw new Error('cannot get access_token');
        }
        return session.accessToken;
    } catch (e) {
        console.log(`[CodeBuddy] getAccessToken failed: ${e}`);
        return undefined;
    }
}

async function showQrWebviewPanel(url: string): Promise<vscode.WebviewPanel> {
    const panel = vscode.window.createWebviewPanel(
        'deviceCodeLogin',
        '扫码登录',
        vscode.ViewColumn.One,
        { enableScripts: true }
    );
    panel.webview.html = await getQrWebviewHtml(url);
    return panel;
}


async function getQrWebviewHtml(url: string) {
    const qrDataUrl = await qrcode.toDataURL(url);

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>扫码登录</title>
</head>
<body>
    <h2>请使用手机飞书扫码登录：</h2>
    <div id="qrcode">
        <a data-pswp-width='512' data-pswp-height='512' target='_blank' href="${qrDataUrl}"><img src="${qrDataUrl}" alt="二维码" style="width: 256px; height: 256px;" /></a>
    </div>
</body>
</html>
    `;
}
