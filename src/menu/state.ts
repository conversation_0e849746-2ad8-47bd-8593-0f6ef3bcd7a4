import { isCopilotEnabled, onConfigChanged } from '@/config';
import { agent, AgentEvent } from '@/agent';
import { Disposable } from 'vscode';
import { MENU_STATE as STATE } from '.';
import { AgentAuthUpdatedEvent } from '@/agent/agent';
import { consoleDebug } from '@/extension';

export function initState(): Disposable[] {
  agent().on('completing:updated', handleAgentEvent);
  agent().on('status:changed', handleAgentEvent);
  agent().on('auth:updated', handleAgentEvent);

  return [
    onConfigChanged(handleConfigChange),
  ];
}

function handleConfigChange() {
  STATE.patch({
    enable: isCopilotEnabled()
  });
}

function handleAgentEvent(event: AgentEvent) {
  consoleDebug('on agent event', event);

  switch (event.kind) {
    case 'completing:updated':
      STATE.patch({});
      break;
    case 'status:changed':
      STATE.patch({
        status: agent().status
      });
      break;
    case 'auth:updated':
      STATE.patch({
        auth: (event as AgentAuthUpdatedEvent).auth
      });
      break;
    default:
      console.warn(`No handler for agent event: ${(event as any).kind}`);
  }
}
