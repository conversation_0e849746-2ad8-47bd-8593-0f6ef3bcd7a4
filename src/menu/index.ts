import * as vscode from 'vscode';
import { BarItemStatus, initStatusBar, toStatus<PERSON>abel } from './status-bar';
import { initState } from './state';
import { telemetry } from '@/telemetry';
import {
  CMD_AGENT_CHECK_UPGRADE,
  CMD_AUTH_LOGIN,
  CMD_AUTH_LOGOUT,
  CMD_CHECK_UPGRADE,
  CMD_OPEN_DOCUMENTATION,
  CMD_OPEN_LOGGING,
  CMD_OPEN_SETTINGS,
  CMD_PLUGIN_CHECK_UPGRADE,
  CMD_SHOW_DETAILS,
  CMD_TOGGLE_ENABLED,
  CMD_AGENT_RESTART,
  CMD_CODEFACTORY_CHECK_UPGRADE
} from '@/commands';
import { appVersion } from '@/extension';
import { agentVersion } from '@/agent';
import { getCopilotConf } from '@/config';
import { StateSubject } from '@/utils';
import { AuthStatus } from '@/agent/lsp-client';
import { codefactoryVersion } from '@/upgrade/check';

export const MENU_STATE = new StateSubject<MenuState>({
  status: 'loading',
  enable: getCopilotConf().get('enabled', true),
  auth: {
    authorized: false,
  }
});

interface MenuItem extends vscode.QuickPickItem {
  cmd?: string;
}

const MENU_SEPARATOR: MenuItem = {
  kind: vscode.QuickPickItemKind.Separator,
  label: ''
};

export async function initMenu(
  _context: vscode.ExtensionContext
): Promise<vscode.Disposable[]> {
  return [
    ...initStatusBar(_context),
    ...initState(),
    { dispose: MENU_STATE.complete },
  ];
}

export type MenuState = {
  enable: boolean;
  auth: AuthStatus;
  status: BarItemStatus;
};

export function isAuthorized(): boolean {
  return MENU_STATE.value.auth.authorized;
}

function buildMenu(): MenuItem[] {
  const state = MENU_STATE.value;
  const authorized = state.auth.authorized;
  const loading = state.status === 'loading';
  const upgrading = state.status === 'upgrading';

  const account = state.auth.account;
  const agentAvailable = agentVersion() !== undefined;

  const menu: MenuItem[] = [];

  // Group: Status
  if (authorized) {
    menu.push(
      { label: `$(dashboard)  状态: ${toStatusLabel(state.status)}` },
      {
        label: !state.enable ? '$(run)  开启代码补全' : '$(stop)  禁用代码补全',
        cmd: CMD_TOGGLE_ENABLED
      },
    );
  }

  // Group: Auth
  menu.push(MENU_SEPARATOR);
  if (authorized && account) {
    menu.push({
      label: `$(account)  ${account.nickname} (${account.email})`
    });
  }
  if (authorized) {
    menu.push({
      label: '$(sign-out)  退出登录...',
      cmd: CMD_AUTH_LOGOUT
    });
  }
  if (!authorized && !loading && !upgrading && agentAvailable) {
    menu.push({
      label: '$(sign-in)  登录...',
      cmd: CMD_AUTH_LOGIN
    });
  }

  if (!agentAvailable) {
    menu.push({
      label: '$(account)  正在初始化内核，请稍后...'
    });
  }

  // Group: View
  // if (authorized) {
  //   menu.push(
  //     MENU_SEPARATOR,
  //     {
  //       label: '$(comment-discussion)  聊天窗口...',
  //       cmd: 'li.codebuddy.view.chat.focus'
  //     },
  // {
  //   label: '$(bracket-dot)  补全建议...',
  //   cmd: 'li.codebuddy.suggestions.generate'
  // },
  //   );
  // }

  // Group: Versions
  menu.push(
    MENU_SEPARATOR,
    {
      label: `$(plug)  插件版本: ${appVersion()}`,
      cmd: CMD_PLUGIN_CHECK_UPGRADE
    },
    {
      label: `$(chip)  内核版本: ${agentAvailable ? agentVersion() : '--'}`,
      cmd: agentAvailable ? CMD_AGENT_CHECK_UPGRADE : undefined
    },
    {
      label: `$(circuit-board) CodeFactory版本: ${codefactoryVersion() || '--'}`,
      cmd: CMD_CODEFACTORY_CHECK_UPGRADE
    },
    {
      label: '$(timeline-refresh)  重启插件内核',
      cmd: agentAvailable ? CMD_AGENT_RESTART : undefined
    },
    {
      label: '$(repo-sync)  检查更新...',
      cmd: CMD_CHECK_UPGRADE
    },
  );

  // Group: General
  menu.push(
    MENU_SEPARATOR,
    { label: '$(output)  查看日志...', cmd: CMD_OPEN_LOGGING },
    { label: '$(book)  帮助文档...', cmd: CMD_OPEN_DOCUMENTATION },
    { label: '$(settings-gear)  设置...', cmd: CMD_OPEN_SETTINGS },
    { label: '$(info)  关于...', cmd: CMD_SHOW_DETAILS }
  );
  return menu;
};

export async function showMainMenu() {
  const menu = buildMenu();

  const picked = await vscode.window.showQuickPick(menu, {
    canPickMany: false
  });

  const cmd = picked?.cmd;
  if (!cmd) {
    return;
  }

  telemetry().offer({
    name: 'view:main-menu.picked',
    details: {
      label: picked.label,
      cmd,
    }
  });
  vscode.commands.executeCommand(cmd);
}
