import {
  commands,
  Disposable,
  ExtensionContext,
  MarkdownString,
  StatusBarAlignment,
  ThemeColor,
  window
} from 'vscode';
import { PLUGIN_ID, PLUGIN_LABEL } from '@/config';
import { agent } from '@/agent';
import { MENU_STATE as STATE, MenuState } from '.';
import { CMD_MAIN_MENU_SHOW, CMD_MAIN_STATUS_CLICKED } from '@/commands';
import { notify } from '@/notify';
import { consoleDebug } from '@/extension';

const LABEL = PLUGIN_LABEL;
const ITEM_ID = `${PLUGIN_ID}.status`;
const ITEM = window.createStatusBarItem(ITEM_ID, StatusBarAlignment.Right);

export type BarItemStatus =
  | 'loading'
  | 'upgrading'
  | 'ready'
  | 'failed-start'
  | 'completing'
  | 'disabled'
  | 'disconnected'
  | 'unauthorized';

type StatusItemConf = {
  text: string;
  tooltip: string | MarkdownString;
  color: ThemeColor;
  backgroundColor: ThemeColor;
};

export function toStatusLabel(status: BarItemStatus): string {
  switch (status) {
    case 'loading':
      return '加载中';
    case 'upgrading':
      return '升级中';
    case 'ready':
      return '就绪';
    case 'failed-start':
      return '启动失败';
    case 'completing':
      return '补全中';
    case 'disabled':
      return '已禁用';
    case 'disconnected':
      return '断开连接';
    case 'unauthorized':
      return '未登录';
    default:
      return status;
  }
}

export function initStatusBar(_context: ExtensionContext): Disposable[] {
  ITEM.command = CMD_MAIN_STATUS_CLICKED;
  ITEM.show();

  const onNextState$ = STATE.subscribe({
    next: handleNextState
  });

  return [
    ITEM,
    { dispose: onNextState$.unsubscribe },
  ];
}

const CONF_NORMAL: StatusItemConf = {
  color: new ThemeColor('statusBar.foreground'),
  backgroundColor: new ThemeColor('statusBar.background'),
  text: `${LABEL}`,
  tooltip: `${PLUGIN_LABEL}`
};

const CONF_WARN: StatusItemConf = {
  ...CONF_NORMAL,
  color: new ThemeColor('statusBarItem.warningForeground'),
  backgroundColor: new ThemeColor('statusBarItem.warningBackground')
};

const CONF_ERROR: StatusItemConf = {
  ...CONF_NORMAL,
  color: new ThemeColor('statusBarItem.errorForeground'),
  backgroundColor: new ThemeColor('statusBarItem.errorBackground')
};

const ITEM_STYLES: { [key in BarItemStatus]: StatusItemConf } = {
  loading: {
    ...CONF_NORMAL,
    text: `$(loading~spin) ${LABEL}`,
    tooltip: `${PLUGIN_LABEL} is initializing.`
  },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'failed-start': {
    ...CONF_ERROR,
    text: `$(testing-failed-icon) ${LABEL}`,
    tooltip: `${PLUGIN_LABEL} Cannot start agent.`
  },
  upgrading: {
    ...CONF_NORMAL,
    text: `$(sync~spin) ${LABEL}`,
    tooltip: `${PLUGIN_LABEL} is upgrading.`
  },
  completing: {
    ...CONF_NORMAL,
    text: `$(gear~spin) ${LABEL}`,
    tooltip: `[${PLUGIN_LABEL}] completing.`
  },
  ready: {
    ...CONF_NORMAL,
    text: `$(gear) ${LABEL}`,
    tooltip: `${PLUGIN_LABEL} is providing code suggestions for you.`
  },
  disabled: {
    ...CONF_WARN,
    text: `$(x) ${LABEL}`,
    tooltip: `${PLUGIN_LABEL} is disabled.`
  },
  disconnected: {
    ...CONF_WARN,
    text: `$(plug) ${LABEL}`,
    tooltip: `Cannot connect to ${PLUGIN_LABEL} server. Click to open settings.`
  },
  unauthorized: {
    ...CONF_WARN,
    text: `$(key) ${LABEL}`,
    tooltip: `${PLUGIN_LABEL} server requires authorization. Click to continue.`
  }
};

const INTEVAL_WAIT_MS = 700;
let LATEST_CLICK_MILLIS = 0;
let CLICKED_COUNTER = 0;

export function onMainStatusClicked() {
  commands.executeCommand(CMD_MAIN_MENU_SHOW);
  const now = Date.now();
  if (now - LATEST_CLICK_MILLIS < INTEVAL_WAIT_MS) {
    CLICKED_COUNTER++;
  } else {
    CLICKED_COUNTER = 0;
  }
  LATEST_CLICK_MILLIS = now;
  if (CLICKED_COUNTER >= 3) {
    console.log(`Main status bar item continuous-clicked ${CLICKED_COUNTER} times.`);
    CLICKED_COUNTER = 0;
    notify.whenMainStatusClickedMultiTimes();
  }
}

function handleNextState(state: MenuState) {
  if (state.enable === false) {
    updateStyle('disabled');
    return;
  }

  const status = state.status;
  switch (status) {
    case 'upgrading':
    case 'disconnected':
    case 'loading':
    case 'failed-start':
    case 'unauthorized':
      updateStyle(status);
      return;
    default:
  }

  if (!state.auth.authorized) {
    updateStyle('unauthorized');
    return;
  }

  if (agent().completing) {
    updateStyle('completing');
    return;
  }

  switch (status) {
    case 'ready':
      updateStyle(status);
      break;
    default:
      console.warn(`Ignored status: ${status}`);
  }
}

function updateStyle(status: BarItemStatus) {
  const conf = ITEM_STYLES[status];
  consoleDebug(`Update status style: ${status}`);
  Object.assign(ITEM, conf);
}
