import process from 'process';
import os from 'os';
import {
  ConfigurationChangeEvent,
  Disposable,
  workspace,
  WorkspaceConfiguration
} from 'vscode';
import * as semver from 'semver';

import { envManager } from '@/judge_env';

export const CHANNEL_DEV = 'dev';
export const CHANNEL_ALPHA = 'alpha';
export const CHANNEL_BETA = 'beta';
export const CHANNEL_STABLE = 'stable';

export const VSCODE_CODE = 'vscode';
export const VSCODE_LABEL = 'VS Code';

export const PLUGIN_ID = 'LiAuto.vscode-li-codebuddy';
export const PLUGIN_CODE = 'codebuddy';
export const PLUGIN_LABEL = 'CodeBuddy';

export const CMD = 'li.codebuddy';
export const CONF = 'li.codebuddy';
export const CONF_COPILOT = `${CONF}.copilot`;
export const CONF_CHAT = `${CONF}.chat`;
export const CONF_TESTING = `${CONF}.testing`;

export const ARCH = arch();
export const PLATFORM = platform();
export const PLATFORM_BIN_SUFFIX = platformBinSuffix();

export const WORK_DIR = `${os.homedir()}/.cache/li-${PLUGIN_CODE}`;
export const DOWNLOAD_CACHE_DIR = `${WORK_DIR}/download-caches`;

export const BIN_DIR = `${WORK_DIR}/bin`;

//health check 检查
export const enableHealthCheck = (version: string): boolean => {
  return semver.gte(version, '0.3.45');
};

export const AGENT_VERSION_RANGE = { start: '0.3.48', end: '0.3.99' };

export const CONFIG_DIR = WORK_DIR;
export const FILE_HISTORIES_DIR = `${CONFIG_DIR}/file-histories`;

export const ARTIFACT_GROUP = 'codebuddy-public';
export const ARTIFACT_GRAPHQL_API = `${envManager.artifactHost}/api/graphql`;
export const ARTIFACT_BASE_URL = `${envManager.artifactHost}/api/v4/projects/11709/packages/generic`;
export const ARTIFACT_ALLOW_LIST_URL = `${envManager.artifactHost}/${ARTIFACT_GROUP}/packages/-/raw/main`;

export const CODEFACTORY_DOWNLOAD_URL = `${envManager.s3Host}/ep-zadig-staging/frontend/codefactory/vsix`;

export const DEFAULT_CHAT_PAGE_URL = `${envManager.s3Host}/ep-zadig-staging/frontend/codebuddy-chat/v0.1.5/index.html`;
// export const DEFAULT_CHAT_PAGE_URL = 'http://localhost:3000/';

export const DEFAULT_SUGGUESTION_PANEL_PAGE_URL =
  `${envManager.artifactHost}/api/v4/projects/11709/packages/generic/codebuddy-suggestion-panel/0.1.0/index.html`;

export const DEFAULT_TESTING_PANEL_PAGE_URL =
  `${envManager.artifactHost}/api/v4/projects/11709/packages/generic/codebuddy-testing-panel/0.0.6/index.html`;

export const DOC_URL_BETA =
  'https://li.feishu.cn/docx/UEkndngvPoaKEexRhvIcfbxZnOc';

export const DOC_URL_STABLE =
  'https://li.feishu.cn/docx/BDJpdFC32omVlPxaNZGclBztnCg';

export const COMP_CODE_AGENT = 'codebuddy-agent';
export const COMP_CODE_PLUGIN = 'codebuddy-vscode';
export const COMP_CODE_FACTORY = 'code-factory';
export const COMPONENT_CONFIG = {
  agent: {
    name: 'CodeBuddy 内核',
    code: COMP_CODE_AGENT
  },
  vscode: {
    name: 'CodeBuddy 插件',
    code: COMP_CODE_PLUGIN
  },
  codefactory: {
    name: 'CodeFactory 插件',
    code: COMP_CODE_FACTORY
  }
};


export const AGENT_CONFIG_VERSION_PATH = '/consts/codebuddy/versions/agent_00001.yaml';

export const TESTING_CONFIG_FILENME = '.codebuddy.yml';

export function getGlobalConf(): WorkspaceConfiguration {
  return workspace.getConfiguration(CONF);
}

export function getCopilotConf(): WorkspaceConfiguration {
  return workspace.getConfiguration(CONF_COPILOT);
}

export function getChatConf(): WorkspaceConfiguration {
  return workspace.getConfiguration(CONF_CHAT);
}

export function getTestingConf(): WorkspaceConfiguration {
  return workspace.getConfiguration(CONF_TESTING);
}

export function isCopilotEnabled(): boolean {
  return getCopilotConf().get('enabled', true);
}

export function onConfigChanged(
  listener: (e: ConfigurationChangeEvent) => any,
  thisArgs?: any
): Disposable {
  return workspace.onDidChangeConfiguration(event => {
    if (event.affectsConfiguration(CONF)) {
      listener.call(thisArgs, event);
    }
  });
}

function platformBinSuffix(): string {
  switch (process.platform) {
    case 'win32':
      return '.exe';
    default:
      return '';
  }
}

function platform(): string {
  switch (process.platform) {
    case 'win32':
      return 'windows';
    case 'darwin':
      return 'darwin';
    case 'linux':
      return 'linux';
  }

  throw new Error(
    `Sorry, the platform '${process.platform}' is not supported.`
  );
}

function arch(): string {
  switch (process.arch) {
    case 'arm64':
    case 'aarch64':
      return 'aarch64';
    case 'x64':
    case 'x86_64':
    case 'amd64':
      return 'x86_64';
  }

  throw new Error(
    `Sorry, the architecture '${process.arch}' is not supported.`
  );
}
