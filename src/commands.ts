import * as vscode from 'vscode';
import { commands, Disposable, ExtensionContext } from 'vscode';
import { notify } from './notify';
import {
  CHANNEL_STABLE,
  CMD,
  DOC_URL_BETA,
  DOC_URL_STABLE,
  getCopilotConf,
  PLUGIN_ID
} from './config';
import { telemetry } from './telemetry';
import { logChannel } from '@/log';
import { checkUpgrade } from '@/upgrade/check';
import { agent } from '@/agent';
import { showMainMenu } from '@/menu';
import { onMainStatusClicked } from '@/menu/status-bar';
// import { login } from '@/agent/login';
import {
  ChatAction,
  CodeFactoryAction
} from '@/chat/model';
import { chat } from '@/chat';
import { appChannel } from '@/extension';
import { authLogin, logout } from './auth/auth_provider';

export const CMD_NOOP = `${CMD}.action.noop`;
export const CMD_TOGGLE_ENABLED = `${CMD}.action.copilot.toggleEnabled`;

export const CMD_CHAT = `${CMD}.chat`;

export const CMD_CHECK_UPGRADE = `${CMD}.action.checkUpgrade`;
export const CMD_PLUGIN_CHECK_UPGRADE = `${CMD}.action.plugin.checkUpgrade`;
export const CMD_AGENT_CHECK_UPGRADE = `${CMD}.action.agent.checkUpgrade`;
export const CMD_CODEFACTORY_CHECK_UPGRADE = `${CMD}.action.codefactory.checkUpgrade`;
export const CMD_AGENT_RESTART = `${CMD}.action.agent.restart`;
export const CMD_AGENT_UPGRADE_TO_VERSION = `${CMD}.action.agent.upgrade`;
export const CMD_AGENT_REINSTALL_FORCE = `${CMD}.action.agent.forceReinstall`;

export const CMD_OPEN_SETTINGS = `${CMD}.action.openSettings`;
export const CMD_OPEN_DOCUMENTATION = `${CMD}.action.openDocumentation`;
export const CMD_OPEN_LOGGING = `${CMD}.action.openLogs`;
export const CMD_SHOW_DETAILS = `${CMD}.action.showDetails`;

export const CMD_AUTH_LOGIN = `${CMD}.auth.login`;
// export const CMD_AUTH_LOGIN_CALLBACK = `${CMD}.auth.login.callback`;
export const CMD_AUTH_LOGOUT = `${CMD}.auth.logout`;
export const CMD_AUTH_LOGOUT_CONFIRMED = `${CMD}.auth.logout.confirmed`;

export const CMD_MAIN_MENU_SHOW = `${CMD}.main-menu.show`;
export const CMD_MAIN_STATUS_CLICKED = `${CMD}.main-status.clicked`;

export const CODE_FACTORY_CMD_EXPLAIN = 'code-factory.explainCode';
export const CODE_FACTORY_CMD_FIX = 'code-factory.fixCode';
export const CODE_FACTORY_CMD_IMPROVE = 'code-factory.improveCode';

const cmd: { [key: string]: (...args: any[]) => any } = {};

cmd[CMD_NOOP] = () => {
  // Do nothing.
};

cmd[CMD_MAIN_MENU_SHOW] = () => {
  showMainMenu();
};

cmd[CMD_MAIN_STATUS_CLICKED] = () => {
  onMainStatusClicked();
};

cmd[CMD_TOGGLE_ENABLED] = () => {
  const copilotConf = getCopilotConf();
  const enabled = copilotConf.get('enabled', true);
  copilotConf.update('enabled', !enabled, true, false);
  telemetry().offer({
    name: 'view:cmd-copilot-toggle-enabled',
    details: {
      enabled: !enabled
    }
  });
};

cmd[CMD_CHECK_UPGRADE] = async () => {
  commands.executeCommand(CMD_AGENT_CHECK_UPGRADE);
  commands.executeCommand(CMD_PLUGIN_CHECK_UPGRADE);
  commands.executeCommand(CMD_CODEFACTORY_CHECK_UPGRADE);
};

cmd[CMD_PLUGIN_CHECK_UPGRADE] = async () => {
  checkUpgrade('vscode');
};

cmd[CMD_AGENT_CHECK_UPGRADE] = async () => {
  checkUpgrade('agent');
};

cmd[CMD_CODEFACTORY_CHECK_UPGRADE] = async () => {
  checkUpgrade('codefactory');
};

cmd[CMD_OPEN_SETTINGS] = () => {
  commands.executeCommand('workbench.action.openSettings', `@ext:${PLUGIN_ID}`);
  telemetry().offer({
    name: 'view:cmd-open-settings'
  });
};

cmd[CMD_SHOW_DETAILS] = () => {
  notify.showPluginDetails();
};

cmd[CMD_OPEN_LOGGING] = () => {
  logChannel.show();
};

cmd[CMD_OPEN_DOCUMENTATION] = () => {
  let url;
  switch (appChannel()) {
    case 'rc':
    case 'release':
    case CHANNEL_STABLE:
      url = vscode.Uri.parse(DOC_URL_STABLE);
      break;
    default:
      url = vscode.Uri.parse(DOC_URL_BETA);
  }

  vscode.env.openExternal(url).then(success => {
    if (success) {
      console.log('Browser opened successfully!');
    } else {
      console.log('Failed to open browser.');
    }
  });
};

//  ===> Auth Commands
// cmd[CMD_AUTH_LOGIN_CALLBACK] = () => {
//   agent().deviceAuthCallback();
// };

cmd[CMD_AUTH_LOGIN] = async () => {
  // login();
  await authLogin();
};

cmd[CMD_AUTH_LOGOUT] = () => {
  notify.confirmLogout();
};

cmd[CMD_AUTH_LOGOUT_CONFIRMED] = async () => {
  // agent().logout();
  await logout();
};
// <=== Auth Commands

//  ===> Chat Commands
cmd[`${CMD_CHAT}.explain`] = obj => {
  chat().triggerCodeFactoryAction(CodeFactoryAction.explain, obj);
};

cmd[`${CMD_CHAT}.fix`] = (obj) => {
  chat().triggerCodeFactoryAction(CodeFactoryAction.fix, obj);
};

// cmd[`${CMD_CHAT}.codegen.doc`] = obj => {
//   chat().triggerAction(ChatAction.codegenDoc, obj);
// };

// cmd[`${CMD_CHAT}.codegen.unit-test`] = obj => {
//   chat().triggerAction(ChatAction.codegenUnitTest, obj);
// };

cmd[`${CMD_CHAT}.refactor`] = obj => {
  chat().triggerCodeFactoryAction(CodeFactoryAction.improve, obj);
};

// <=== Chat Commands

// async function handleUri(uri: vscode.Uri) {
//   console.log('handleUri', uri);
//   switch (uri.path) {
//     case '/ping':
//       break;
//     case '/auth/login/callback':
//       commands.executeCommand(CMD_AUTH_LOGIN_CALLBACK);
//       break;
//     default:
//       console.log('Unknown uri: ', uri);
//   }
// }

export async function initCommands(_context: ExtensionContext): Promise<Disposable[]> {
  return [
    ...Object.keys(cmd).map(key =>
      commands.registerCommand(key, cmd[key])
    ),
    // vscode.window.registerUriHandler({
    //   handleUri,
    // }),
  ];
}
