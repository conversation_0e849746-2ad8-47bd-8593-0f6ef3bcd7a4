import { describe, it, expect, beforeEach, afterAll, vi } from 'vitest';
import { appEnv, isRedZone, getEnvManager, envManager, IS_RED_ZONE, APP_ENV } from '../judge_env';

describe('judge_env', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    vi.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('appEnv()', () => {
    it('should return empty string when CI_COMMIT_TAG is undefined', () => {
      delete process.env.CI_COMMIT_TAG;
      expect(appEnv()).toBe('');
    });

    it('should return empty string when CI_COMMIT_TAG has no +', () => {
      process.env.CI_COMMIT_TAG = 'v1.0.0';
      expect(appEnv()).toBe('');
    });

    it('should return build part when CI_COMMIT_TAG has +', () => {
      process.env.CI_COMMIT_TAG = 'v1.5.0-alpha+redzone';
      expect(appEnv()).toBe('redzone');
    });

    it('should return build part when CI_COMMIT_TAG has multiple dots', () => {
      process.env.CI_COMMIT_TAG = 'v1.5.0-alpha.1+redzone.1';
      expect(appEnv()).toBe('redzone.1');
    });
  });

  describe('isRedZone()', () => {
    it('should return false when appEnv is empty', () => {
      delete process.env.CI_COMMIT_TAG;
      expect(appEnv()).toBe('');
      expect(isRedZone()).toBe(false);
    });

    it('should return false when appEnv does not contain redzone', () => {
      process.env.CI_COMMIT_TAG = 'v1.0.0';
      expect(appEnv()).toBe('');
      expect(isRedZone()).toBe(false);
    });

    it('should return true when appEnv contains redzone', () => {
      process.env.CI_COMMIT_TAG = 'v1.5.0-alpha+redzone';
      expect(appEnv()).toBe('redzone');
      expect(isRedZone()).toBe(true);
    });
  });

  describe('getEnvManager()', () => {
    it('should return redZoneEnvManager when isRedZone is true', () => {
      process.env.CI_COMMIT_TAG = 'v1.5.0-alpha+redzone';
      const manager = getEnvManager();
      expect(manager.defaultCopilotApi).toBe('https://coding-copilot-redzone.ep.chehejia.com/api/copilot');
    });

    it('should return commonEnvManager when isRedZone is false', () => {
      process.env.CI_COMMIT_TAG = 'v1.5.0-alpha';
      const manager = getEnvManager();
      expect(manager.defaultCopilotApi).toBe('https://portal-k8s-prod.ep.chehejia.com/api/copilot');
    });
  });

  describe('exported constants', () => {
    it('should export envManager', () => {
      expect(envManager).toBeDefined();
    });

    it('should export IS_RED_ZONE', () => {
      expect(IS_RED_ZONE).toBeDefined();
    });

    it('should export APP_ENV', () => {
      expect(APP_ENV).toBeDefined();
    });
  });
});
