import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import * as vscode from 'vscode';
import { Disposable } from 'vscode';
import {
  initContextManager,
  updateAllContexts,
  updateInlineChatContext,
  updateBlockEditContext
} from '../../context/setContext';
import { onConfigChanged, getCopilotConf, CHANNEL_DEV } from '@/config';

// Mock vscode module
vi.mock('vscode', () => ({
  commands: {
    executeCommand: vi.fn()
  },
  Disposable: vi.fn()
}));

// Mock config module
vi.mock('@/config', () => ({
  onConfigChanged: vi.fn(),
  getCopilotConf: vi.fn(),
  CHANNEL_DEV: 'dev'
}));

// Mock extension module
vi.mock('@/extension', () => ({
  appChannel: vi.fn()
}));

describe('setContext', () => {
  let mockExecuteCommand: Mock;
  let mockOnConfigChanged: Mock;
  let mockGetCopilotConf: Mock;
  let mockConfigGet: Mock;
  let mockDisposable: Disposable;
  let mockAppChannel: Mock;

  beforeEach(async () => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup vscode mocks
    mockExecuteCommand = vi.mocked(vscode.commands.executeCommand);

    // Setup config mocks
    mockOnConfigChanged = vi.mocked(onConfigChanged);
    mockGetCopilotConf = vi.mocked(getCopilotConf);

    // Setup extension mocks
    const extensionModule = await import('@/extension');
    mockAppChannel = vi.mocked(extensionModule.appChannel);
    mockAppChannel.mockReturnValue('stable'); // Default to stable channel

    // Setup config.get mock
    mockConfigGet = vi.fn();

    // Setup disposable mock
    mockDisposable = { dispose: vi.fn() } as Disposable;
    mockOnConfigChanged.mockReturnValue(mockDisposable);

    // Setup getCopilotConf to return an object with get method
    mockGetCopilotConf.mockReturnValue({
      get: mockConfigGet
    } as any);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('initContextManager', () => {
    it('should initialize context manager and return disposables', () => {
      const disposables = initContextManager();

      // Should call updateAllContexts initially
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(2); // Called by both update functions
      expect(mockExecuteCommand).toHaveBeenCalledTimes(3); // 1 by inline chat + 2 by block edit

      // Should setup config change listener
      expect(mockOnConfigChanged).toHaveBeenCalledTimes(1);
      expect(mockOnConfigChanged).toHaveBeenCalledWith(expect.any(Function));

      // Should return array with disposable
      expect(disposables).toEqual([mockDisposable]);
    });

    it('should call updateAllContexts when config changes', () => {
      // Get the callback function passed to onConfigChanged before clearing mocks
      initContextManager();
      const configChangeCallback = mockOnConfigChanged.mock.calls[0][0];

      // Clear previous calls
      vi.clearAllMocks();
      mockGetCopilotConf.mockReturnValue({
        get: mockConfigGet
      } as any);
      mockAppChannel.mockReturnValue('stable');

      // Call the callback to simulate config change
      configChangeCallback();

      // Should call updateAllContexts again
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(2);
      expect(mockExecuteCommand).toHaveBeenCalledTimes(3); // 1 by inline chat + 2 by block edit
    });
  });

  describe('updateAllContexts', () => {
    it('should call both updateInlineChatContext and updateBlockEditContext', () => {
      updateAllContexts();

      // Should call getCopilotConf twice (once for each update function)
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(2);

      // Should call executeCommand 3 times (1 by inline chat + 2 by block edit)
      expect(mockExecuteCommand).toHaveBeenCalledTimes(3);
    });
  });

  describe('updateInlineChatContext', () => {
    it('should set inline chat context to true when enabled', () => {
      mockConfigGet.mockReturnValue(true);

      updateInlineChatContext();

      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('inlineChat.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.inlineChat.enabled',
        true
      );
    });

    it('should set inline chat context to false when disabled', () => {
      mockConfigGet.mockReturnValue(false);

      updateInlineChatContext();

      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('inlineChat.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.inlineChat.enabled',
        false
      );
    });

    it('should set inline chat context to false when config returns undefined', () => {
      mockConfigGet.mockReturnValue(undefined);

      updateInlineChatContext();

      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('inlineChat.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.inlineChat.enabled',
        false
      );
    });

    it('should convert truthy values to true using double negation', () => {
      mockConfigGet.mockReturnValue('some string'); // truthy value

      updateInlineChatContext();

      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.inlineChat.enabled',
        true
      );
    });
  });

  describe('updateBlockEditContext', () => {
    it('should set block edit context to true when enabled', () => {
      mockConfigGet.mockReturnValue(true);
      mockAppChannel.mockReturnValue('stable');

      updateBlockEditContext();

      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('nextEdit.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledTimes(2);
      expect(mockExecuteCommand).toHaveBeenNthCalledWith(
        1,
        'setContext',
        'li.codebuddy.copilot.nextEdit.enabled',
        true
      );
      expect(mockExecuteCommand).toHaveBeenNthCalledWith(
        2,
        'setContext',
        'li.codebuddy.copilot.nextEdit.acceptable',
        false
      );
    });

    it('should set block edit context to false when disabled', () => {
      mockConfigGet.mockReturnValue(false);
      mockAppChannel.mockReturnValue('stable');

      updateBlockEditContext();

      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('nextEdit.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledTimes(2);
      expect(mockExecuteCommand).toHaveBeenNthCalledWith(
        1,
        'setContext',
        'li.codebuddy.copilot.nextEdit.enabled',
        false
      );
      expect(mockExecuteCommand).toHaveBeenNthCalledWith(
        2,
        'setContext',
        'li.codebuddy.copilot.nextEdit.acceptable',
        false
      );
    });

    it('should set block edit context to false when config returns undefined', () => {
      mockConfigGet.mockReturnValue(undefined);
      mockAppChannel.mockReturnValue('stable');

      updateBlockEditContext();

      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('nextEdit.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledTimes(2);
      expect(mockExecuteCommand).toHaveBeenNthCalledWith(
        1,
        'setContext',
        'li.codebuddy.copilot.nextEdit.enabled',
        false
      );
      expect(mockExecuteCommand).toHaveBeenNthCalledWith(
        2,
        'setContext',
        'li.codebuddy.copilot.nextEdit.acceptable',
        false
      );
    });

    it('should convert truthy values to true using double negation', () => {
      mockConfigGet.mockReturnValue(1); // truthy value
      mockAppChannel.mockReturnValue('stable');

      updateBlockEditContext();

      expect(mockExecuteCommand).toHaveBeenNthCalledWith(
        1,
        'setContext',
        'li.codebuddy.copilot.nextEdit.enabled',
        true
      );
      expect(mockExecuteCommand).toHaveBeenNthCalledWith(
        2,
        'setContext',
        'li.codebuddy.copilot.nextEdit.acceptable',
        false
      );
    });

    it('should enable nextEdit when app channel is dev', () => {
      mockConfigGet.mockReturnValue(false);
      mockAppChannel.mockReturnValue('dev');

      updateBlockEditContext();

      expect(mockExecuteCommand).toHaveBeenNthCalledWith(
        1,
        'setContext',
        'li.codebuddy.copilot.nextEdit.enabled',
        true // Should be true because appChannel is 'dev'
      );
    });

    it('should set acceptable parameter correctly', () => {
      mockConfigGet.mockReturnValue(false);
      mockAppChannel.mockReturnValue('stable');

      updateBlockEditContext(true);

      expect(mockExecuteCommand).toHaveBeenNthCalledWith(
        2,
        'setContext',
        'li.codebuddy.copilot.nextEdit.acceptable',
        true
      );
    });
  });

  describe('error handling', () => {
    it('should handle errors when executeCommand throws', () => {
      mockExecuteCommand.mockRejectedValue(new Error('Command failed'));
      mockConfigGet.mockReturnValue(true);
      mockAppChannel.mockReturnValue('stable');

      // Should not throw
      expect(() => updateInlineChatContext()).not.toThrow();
      expect(() => updateBlockEditContext()).not.toThrow();
    });

    it('should handle errors when getCopilotConf throws', () => {
      mockGetCopilotConf.mockImplementation(() => {
        throw new Error('Config failed');
      });

      // Should throw since we don't handle this error in the implementation
      expect(() => updateInlineChatContext()).toThrow('Config failed');
      expect(() => updateBlockEditContext()).toThrow('Config failed');
    });

    it('should handle errors when appChannel throws', () => {
      mockGetCopilotConf.mockReturnValue({ get: vi.fn().mockReturnValue(false) });
      mockAppChannel.mockImplementation(() => {
        throw new Error('AppChannel failed');
      });

      // Should throw since we don't handle this error in the implementation
      expect(() => updateBlockEditContext()).toThrow('AppChannel failed');
    });
  });
});
