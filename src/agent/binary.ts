import {
  AGENT_VERSION_RANGE,
  ARCH,
  ARTIFACT_BASE_URL,
  BIN_DIR,
  COMP_CODE_AGENT,
  DOWNLOAD_CACHE_DIR,
  PLATFORM,
  PLATFORM_BIN_SUFFIX,
  PLUGIN_CODE
} from '@/config';
import * as fse from 'fs-extra';
import { downloadToFile } from '@/utils/download';
import { tgz } from 'compressing';
import * as semver from 'semver';
import { SemVer } from 'semver';
import { execSync } from 'child_process';
import { getAvailableLatestVersion } from '@/upgrade/check';

let VERSION: string | undefined;

export function agentVersion(): string | undefined {
  return VERSION;
}

export function isUpgrade(version: string): boolean {
  if (!version || VERSION === version) {
    return false;
  }
  if (!VERSION) {
    return true;
  }
  return semver.gt(version, VERSION);
}

export async function upgradeBinary(
  version: string,
  force?: boolean
): Promise<string> {
  if (!force && !isUpgrade(version)) {
    throw new Error(
      `Cannot upgrade: target version is not bigger than current: !(${version} > ${VERSION})`
    );
  }

  await download(version);
  VERSION = version;
  return binOfVersion(version);
}

export async function ensureBinary(): Promise<string> {
  fse.ensureDirSync(BIN_DIR);

  const localVersions = fse.readdirSync(BIN_DIR);
  console.log('Found local versions:', localVersions);

  for (const version of await sortedCompatibleVersions(localVersions)) {
    const bin = binOfVersion(version);
    const valid = await verifyBinary(bin);
    if (valid) {
      console.log(`Using local binary with version ${version}:`, bin);
      VERSION = version;
      return bin;
    }
  }
  const compVer = (await getAvailableLatestVersion('agent')) || '';
  return await upgradeBinary(compVer);
}

async function sortedCompatibleVersions(raw: string[]): Promise<string[]> {
  let versions: SemVer[] = raw
    .map(version => semver.parse(version))
    .filter(version => version !== null) as SemVer[];

  const { start, end } = AGENT_VERSION_RANGE;
  return versions
    .sort((a, b) => semver.compare(b, a))
    .filter(v => semver.gte(v, start) && semver.lte(v, end))
    .map(version => version.version);
}

function binaryPackageName(version: string): string {
  if (PLATFORM === 'windows') {
    return `codebuddy-${version}-windows-x86_64`;
  }
  return `codebuddy-${version}-${PLATFORM}-${ARCH}`;
}

async function download(version: string) {
  const packageName = binaryPackageName(version);
  const url = `${ARTIFACT_BASE_URL}/${COMP_CODE_AGENT}/${version}/${packageName}.tgz`;
  const targetTgz = `${DOWNLOAD_CACHE_DIR}/${packageName}-${new Date().getTime()}.tgz`;
  const targetDir = `${BIN_DIR}/${version}`;

  console.log('Downloading binary...', url, targetTgz);
  fse.ensureDirSync(DOWNLOAD_CACHE_DIR);
  fse.ensureDirSync(targetDir);

  await downloadToFile(url, targetTgz);
  console.log('Downloaded binary to', targetTgz);

  fse.emptyDirSync(targetDir);

  await tgz.decompress(targetTgz, targetDir);
  console.log('Decompressed binary to', targetDir);

  fse.removeSync(targetTgz);
  console.log('Removed downloaded binary', targetTgz);

  const bin = binOfVersion(version);
  const valid = await verifyBinary(bin);
  if (!valid) {
    throw new Error(`Downloaded binary is invalid: ${bin}`);
  }
}

function binOfVersion(version: string): string {
  return `${BIN_DIR}/${version}/${PLUGIN_CODE}${PLATFORM_BIN_SUFFIX}`;
}

async function verifyBinary(bin: string): Promise<boolean> {
  if (!fse.existsSync(bin)) {
    return false;
  }
  const cmd = `${bin} version`;
  try {
    const v = execSync(cmd).toString().trim();
    return semver.valid(v) !== null;
  } catch (error: any) {
    return false;
  }
}
