// import { agent } from '@/agent/index';
// import vscode, { Uri } from 'vscode';
// import { notify } from '@/notify';
// import { interval, switchMap, takeUntil, takeWhile, timer } from 'rxjs';

// export async function login() {
//   agent().deviceRegister().then(res => {
//     console.log('Register device responsed: ', res);
//     const url = Uri.parse(res.loginUrl);
//     console.log(`[Auth] auth url: ${url}`);
//     openLoginPage(url);
//   }).catch(err => {
//     console.log('Failed to register device!', err);
//     notify.whenLoginFailed();
//   });
// }

// function openLoginPage(url: Uri) {
//   vscode.env.openExternal(url).then(opened => {
//     if (!opened) {
//       notify.whenLoginFailed('未能通过浏览器打开登录页面');
//       return;
//     }
//     notify.confirmLoginCallback();
//     startFallbackLoginCheck();
//   });
// }

// function startFallbackLoginCheck() {
//   interval(5 * 1000)
//     .pipe(
//       switchMap(() => agent().deviceAuthTry()),
//       takeWhile(result => !result),
//       takeUntil(timer(20 * 60 * 1000)),
//     )
//     .subscribe({
//       next: result => {
//         console.log('Authed by fallback', result);
//       },
//       complete: () => {
//         console.log('Auth fallback login checking completed.');
//       },
//       error: (err) => {
//         console.error('Auth: Something wrong happened:', err);
//       }
//     });
// }
