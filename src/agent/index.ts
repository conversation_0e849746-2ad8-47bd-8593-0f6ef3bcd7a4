import { commands, Disposable, env, ExtensionContext, version } from 'vscode';
import {
  onConfigChanged,
  PLUGIN_LABEL,
  enableHealthCheck,
  VSCODE_LABEL
} from '@/config';
import { Agent } from './agent';
import { ClientParams, PlatformParams } from '@/agent/lsp-client';
import {
  CMD_AGENT_REINSTALL_FORCE,
  CMD_AGENT_UPGRADE_TO_VERSION,
  CMD_AGENT_RESTART
} from '@/commands';
import { appChannel, appVersion } from '@/extension';
import { getAvailableLatestVersion } from '@/upgrade/check';
import { agentVersion } from './binary';
import { sendEvent  } from '@/telemetry';

export type {
  Choice, CompletionResponse, StatusVO, DeviceRegLspVO
} from './lsp-client';

export type {
  Agent,
  AgentAuthUpdatedEvent,
  AgentEvent,
  AgentGenericEvent,
  AgentStatus,
  AgentStatusChangedEvent
} from './agent';

export { agentVersion } from './binary';

let AGENT: Agent | undefined;

export function agent(): Agent {
  if (!AGENT) {
    throw new Error(`[${PLUGIN_LABEL}] Agent is not initialized`);
  }
  return AGENT;
}

export function registerHealthCheck(){
  // agent health check 3 s
  const healchCheckInterval = 3 * 1000;
  if (!enableHealthCheck(agentVersion()!)) {
    return;
  }
  let failedTimes = 0;
  setInterval(async () => {
    const value = await agent().healthStatus();
    console.log('agent health check', value);
    if (value?.status && value.status === 'ok') {
      console.log('agent is health');
      failedTimes = 0;
    } else if (failedTimes >= 3) {
      console.log(
        `agent is not health, failed_times: ${failedTimes}, restart agent`
      );
      sendEvent({ name: 'agent:health-check-failed' });
      failedTimes = 0;
      await agent().restart();
    } else {
      console.log(
        `agent is not health, failed_times: ${failedTimes}, retry after 3s`
      );
      failedTimes += 1;
    }
  }, healchCheckInterval);
}

export async function forceReinstall() {
  const version = await getAvailableLatestVersion('agent');
  if (!version) {
    throw new Error('No available version.');
  }
  console.log(`Will reinstall agent with version: ${version}`);
  agent().upgrade(version, true);
}

export async function initAgent(_context: ExtensionContext): Promise<Disposable[]> {
  if (AGENT) {
    return [];
  }

  const client: ClientParams = {
    version: appVersion(),
    channel: appChannel()
  };

  const platform: PlatformParams = {
    family: VSCODE_LABEL,
    name: VSCODE_LABEL,
    version: version,
    versionMajor: version.split('.')[0],
    lang: env.language,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  };

  const agent = new Agent({
    client,
    platform
  });

  AGENT = agent;
  return [
    agent,
    commands.registerCommand(CMD_AGENT_UPGRADE_TO_VERSION, version =>
      agent.upgrade(version)
    ),
    commands.registerCommand(CMD_AGENT_REINSTALL_FORCE, forceReinstall),
    commands.registerCommand(CMD_AGENT_RESTART, async () => await agent.restart()),
    onConfigChanged(_ => {
      agent.onConfigChanged();
    }),
    {
      dispose(): any {
        AGENT = undefined;
      }
    }
  ];
}
