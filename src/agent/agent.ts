import { EventEmitter } from 'events';
import deepEqual from 'deep-equal';
import code, {
  CancellationToken,
  Disposable,
  Position,
  TextDocument
} from 'vscode';
import {
  LanguageClient,
  TextDocumentIdentifier
} from 'vscode-languageclient/node';
import {
  AuthStatus,
  CompletionAcceptedParams,
  CompletionEventParams,
  CompletionParams,
  CompletionResponse,
  CompletionScene,
  CopilotConfigParams,
  createLspClient,
  restartLspClient,
  DeviceRegLspVO,
  ListCachedCompletionsParams,
  LspConfigParams,
  TelemetryConfigParams,
  upgradeAndRestartLspClient,
  StatusVO,
  AccountVO
} from '@/agent/lsp-client';
import {
  getGlobalConf,
  COMP_CODE_AGENT
} from '@/config';
import { notify } from '@/notify';
import { EventForm, Telemetry, persistentEvent } from '@/telemetry';
import { isAuthorized } from '@/menu';
import { consoleDebug } from '@/extension';
import { getAppState } from '@/state/state'
import { registerHealthCheck } from './index';
import { getCurVersion } from '@/upgrade/check';
import { envManager } from '@/judge_env';
import { getAccount } from '@/auth/auth_provider';
import { logChannel } from '@/log';

// Note: 延迟补全状态变更事件，避免 命中缓存、跳过策略、fail-fast 等机制带来的闪烁
const COMPLETING_EVENT_DELEY_MS = 10;
const COMPLETING_TIMEOUT_MS = 1000 * 5;
const EDIT_COMPLETING_TIMEOUT_MS = 5000 * 5;
const HEALTH_CHECK_TIMEOUT_MS = 1000;
const CLIENT_STOP_TIMEOUT_MS = 1000 * 3;

export type AgentConfig = {
  telemetry: TelemetryConfigParams;
  copilot: CopilotConfigParams;
};

export type AgentStatus =
  | 'loading'
  | 'ready'
  | 'failed-start'
  | 'disconnected'
  | 'upgrading';

export type AgentEventKind =
  | 'completing:updated'
  | 'status:changed'
  | 'auth:updated';

export type AgentGenericEvent = {
  kind: 'completing:updated';
};

export type AgentStatusChangedEvent = {
  kind: 'status:changed';
  status: AgentStatus;
};

export type AgentAuthUpdatedEvent = {
  kind: 'auth:updated';
  auth: AuthStatus;
};

export type AgentEvent =
  | AgentStatusChangedEvent
  | AgentAuthUpdatedEvent
  | AgentGenericEvent;

export interface CompletionForm {
  requestId: string;
  scene: CompletionScene;
  doc: TextDocument;
  position: Position;
  choiceLimit?: number;
  visibleWindow?: {
    start: number,
    end: number,
  }
}

export interface ListCachedCompletionsForm {
  scene: CompletionScene;
  doc: TextDocument;
  position: Position;
}

function resolveConfig(): AgentConfig {
  const conf = getGlobalConf();
  return {
    copilot: {
      model: 'default',
      baseUrl: conf.get<string>('copilot.api', envManager.defaultCopilotApi)
    },
    telemetry: {
      baseUrl: conf.get<string>('telemetry.api', envManager.defaultTelemetryApi)
    }
  };
}

export const timeoutSymobol: unique symbol = Symbol('timeout');
async function timeoutWrapper(
  result: Promise<any>,
  timeout: number
): Promise<any> {
  return await Promise.race([result, new Promise((resolve) => {
    setTimeout(() => {
      resolve(timeoutSymobol);
    }, timeout);
  })]);
}

export class Agent implements Telemetry, Disposable {
  private config!: AgentConfig;
  private params: LspConfigParams;

  private _client: LanguageClient | undefined;

  private _completing = false;
  private _status: AgentStatus = 'loading';

  private _eventEmitter = new EventEmitter();

  get completing(): boolean {
    return this._completing;
  }

  get client(): LanguageClient {
    const client = this._client;
    if (client) {
      return client;
    }
    throw new Error('LSP client is not initialized');
  }

  constructor(lspConfigParams: LspConfigParams) {
    this.params = lspConfigParams;
  }

  dispose() {
    this.stop().catch(e => {
      logChannel.appendLine(`Error during dispose: ${e}`);
    });
    return;
  }


  public get status(): AgentStatus {
    return this._status;
  }

  get readyForCompletion(): boolean {
    return this.status === 'ready';
  }

  public on(event: AgentEventKind, listener: (event: AgentEvent) => void) {
    this._eventEmitter.on(event, listener);
  }

  private emit(event: AgentEvent) {
    consoleDebug('Agent event:', event.kind);
    this._eventEmitter.emit(event.kind, event);
  }

  private set completing(bool: boolean) {
    this._completing = bool;
    setTimeout(
      () => {
        this.emit({ kind: 'completing:updated' });
      },
      bool ? COMPLETING_EVENT_DELEY_MS : 0
    );
  }

  private set status(status: AgentStatus) {
    if (this._status === status) {
      return;
    }
    this._status = status;
    this.emit({ kind: 'status:changed', status });
  }

  public async afterActivated() {
    this.config = resolveConfig();
    try {
      this._client = await createLspClient();
      await this.configLspClient();
      registerHealthCheck();
      this.status = 'ready';
    } catch (e) {
      this.status = 'failed-start';
      console.log('Cannot start agent:', e);
      // notify.whenAgentStartFailed();
    }
  }

  public async stop() {
    if (this._client) {
      try {
        const result = await timeoutWrapper(this._client.stop(), CLIENT_STOP_TIMEOUT_MS);
        if (result === timeoutSymobol) {
          logChannel.appendLine(`language client stop timeout after ${CLIENT_STOP_TIMEOUT_MS}ms`);
        }
      } catch (e) {
        logChannel.appendLine(`language client stop failed: ${e}`);
      } finally {
        this._client = undefined;
      }
    }
  }
  public async restart() {
    this.config = resolveConfig();
    try {
      if (this._client) {
        await this.stop();
      }
      this._client = await restartLspClient();
      await this.configLspClient();
      this.status = 'ready';
    } catch (e) {
      this.status = 'failed-start';
      console.log('Cannot restart agent:', e);
      // notify.whenAgentStartFailed();
    }
  }

  public async upgrade(version: string, force?: boolean) {
    if (!version) {
      console.error('Cannot upgrade: version is empty.');
      return;
    }
    const legacy = this._client;
    const startTime = performance.now();
    const curVersion = getCurVersion(COMP_CODE_AGENT);
    const details = {
      component: COMP_CODE_AGENT,
      timestamp: new Date(Date.now()),
      currentVersion: curVersion || '',
      targetVersion: version,
    };
    try {
      this.status = 'upgrading';
      this._client = await upgradeAndRestartLspClient(legacy, version, force);
      await this.configLspClient();
      this.status = 'ready';
      console.log('Upgraded agent:', version);
      // notify.whenAgentUpgraded(version);
      const endTime = performance.now();
      const event = persistentEvent({
        name: `copilot:${COMP_CODE_AGENT}:upgrade`,
        du: endTime - startTime
      });
      event.end({ details: { ...details, status: 'success' } });
    } catch (e) {
      this.status = 'failed-start';
      console.log('Cannot start agent:', e);
      const endTime = performance.now();
      const event = persistentEvent({
        name: `copilot:${COMP_CODE_AGENT}:upgrade`,
        du: endTime - startTime
      });
      event.end({ details: { ...details, status: 'failure', errMsg: `${e}` } });
      // notify.whenAgentUpgradeFailed(version);
    }
  }

  async configLspClient() {
    const params: LspConfigParams = {
      ...this.params,
      ...this.config
    };

    console.log('LSP config: ', params);
    await this.client.sendRequest('codebuddy/config', params);
    console.log('LSP config changed.');

    await this.authStatus().then((authStatus) => {
      this.emit_auth(authStatus)
    }).catch((error) => {
      console.log('Auth status error: ', error);
    });
  }

  public offer(form: EventForm): void {
    consoleDebug('Telemetry event: ', form.name);
    const event: EventForm = Object.assign(
      {
        time: Date.now(),
        du: 0,
        details: {}
      },
      form
    );
    this._telemetry(event);
  }

  private async _telemetry(event: EventForm) {
    const client = this._client;
    if (!client) {
      console.warn('Client is not ready for telemetry.');
      return;
    }
    try {
      await client.sendRequest('codebuddy/telemetry/event', event);
    } catch (e) {
      console.warn('Telemetry error: ', e);
    }
  }

  public async onConfigChanged() {
    const conf = resolveConfig();
    if (deepEqual(this.config, conf)) {
      return;
    }
    this.config = conf;
    await this.configLspClient();
  }

  asTextDocumentIdentifier(
    textDocument: code.TextDocument
  ): TextDocumentIdentifier {
    return this.client.code2ProtocolConverter.asTextDocumentIdentifier(
      textDocument
    );
  }

  public async healthStatus(): Promise<StatusVO> {
    const resp: Promise<StatusVO> = this.client.sendRequest('codebuddy/agent/health', {});
    try {
      return await timeoutWrapper(resp, HEALTH_CHECK_TIMEOUT_MS);
    } catch (e: any) {
      console.log('health check is error', e.message);
      return new Promise<StatusVO>(resolve => resolve({}));
    }
  }

  // public async deviceRegister(): Promise<DeviceRegLspVO> {
  //   return await this.client.sendRequest('codebuddy/auth/device/register', {});
  // }

  // public async deviceAuthCallback() {
  //   return await this.client.sendRequest('codebuddy/auth/device/callback', {})
  //     .then((resp) => {
  //       if (isAuthorized()) {
  //         console.log('Ignore response of device-auth, already authorized');
  //         return;
  //       }

  //       const auth = resp as AuthStatus;
  //       this.emit_auth(auth)
  //       if (auth.authorized === true) {
  //         notify.whenLogin(auth);
  //       } else {
  //         notify.whenLoginFailed();
  //       }
  //     }).catch((error) => {
  //       console.log('Device auth callback error: ', error);
  //       notify.whenLoginFailed();
  //     });
  // }

  // public async deviceAuthTry() {
  //   return await this.client.sendRequest('codebuddy/auth/device/try', {})
  //     .then((resp) => {
  //       // console.log('Got auth with try: ', resp);
  //       if (isAuthorized()) {
  //         console.log('Ignore response of try-device-auth, already authorized');
  //         return true;
  //       }

  //       const auth = resp as AuthStatus;
  //       if (auth.authorized === true) {
  //         notify.whenLogin(auth);
  //         this.emit_auth(auth)
  //       }
  //       return auth.authorized;
  //     });
  // }

  public async authStatus(): Promise<AuthStatus> {
    let authStatus: AuthStatus = {
      authorized: false,
    };

    try {
      const account = await getAccount();
      if (account) {
        const accountVO: AccountVO = {
          openid: account.openid,
          email: account.email,
          nickname: account.nickname
        };
        authStatus = {
          authorized: true,
          account: accountVO
        };
        return authStatus;
      }
    } catch (e) {
      console.log(`getAccount error: ${e}, will try codebuddy/auth/status`);
    }

    try {
      const res: AuthStatus = await this.client.sendRequest('codebuddy/auth/status', {});
      if (res && res.authorized) {
        return res;
      }
    } catch (e) {
      console.log(`codebuddy/auth/status error: ${e}, cannot get account info`);
    };

    return authStatus;
  }

  public async account(accountVO: AccountVO) {
    return await this.client.sendRequest('codebuddy/auth/account', {
      openid: accountVO.openid,
      email: accountVO.email,
      nickname: accountVO.nickname
    });
  }

  emit_auth(auth: AuthStatus) {
    this.emit({ kind: 'auth:updated', auth })
    getAppState().setIsLogined(auth?.authorized)
  }

  public async logout(): Promise<void> {
    return await this.client.sendRequest('codebuddy/auth/signout', {});
    // return await this.client.sendRequest('codebuddy/auth/logout', {})
    //   .then((resp) => {
    //     const auth = resp as AuthStatus;
    //     this.emit_auth(auth)
    //     if (auth.authorized === false) {
    //       notify.whenLogout();
    //     } else {
    //       console.log('Logout failed: ', auth);
    //     }
    //   });
  }

  public async emitCompletionAccepted(params: CompletionAcceptedParams) {
    await this.client.sendRequest('codebuddy/completion/accepted', params);
  }

  public async emitCompletionEvent(params: CompletionEventParams) {
    await this.client.sendRequest('codebuddy/completion/event', params);
  }

  public async listCachedCompletions(token: CancellationToken, form: ListCachedCompletionsForm) {
    const params: ListCachedCompletionsParams = {
      textDocument: this.asTextDocumentIdentifier(form.doc),
      scene: form.scene,
      position: form.position,
    };
    const result = await (
      this.client.sendRequest(
        'codebuddy/completion/cached',
        params, token
      ) as Promise<Array<CompletionResponse>>
    );
    return result;
  }

  public async resolveCompletion(token: CancellationToken, form: CompletionForm) {
    let params: CompletionParams = {
      textDocument: this.asTextDocumentIdentifier(form.doc),
      model: this.config.copilot.model,
      requestId: form.requestId,
      scene: form.scene,
      position: form.position,
      choiceLimit: form.choiceLimit || 1,
    };
    this.completing = true;
    const response: Promise<CompletionResponse> = this.client.sendRequest(
      'codebuddy/completion/resolve',
      params,
      token
    );
    const result: CompletionResponse = await Promise.race([
      response,
      new Promise<CompletionResponse>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Completion timeout'));
        }, COMPLETING_TIMEOUT_MS);
      })
    ]).catch(err => {
      console.warn('Error resolving completion: ', err);
      return {
        id: 'error',
        requestId: form.requestId,
        error: err.message
      } as CompletionResponse;
    }).finally(() => {
      this.completing = false;
    });
    return result;
  }

  public async predictCompletion(form: CompletionForm) {
    let params: CompletionParams = {
      textDocument: this.asTextDocumentIdentifier(form.doc),
      model: this.config.copilot.model,
      requestId: form.requestId,
      scene: form.scene,
      position: form.position,
      choiceLimit: form.choiceLimit || 1,
      visibleWindow: form.visibleWindow,
    };
    this.completing = true;
    const response: Promise<CompletionResponse> = this.client.sendRequest(
      'codebuddy/completion/predict',
      params
    );
    const result: CompletionResponse = await Promise.race([
      response,
      new Promise<CompletionResponse>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Completion timeout'));
        }, EDIT_COMPLETING_TIMEOUT_MS);
      })
    ]).catch(err => {
      console.warn('Error resolving predictCompletion: ', err);
      return {
        id: 'error',
        requestId: form.requestId,
        error: err.message
      } as CompletionResponse;
    }).finally(() => {
      this.completing = false;
    });
    console.log('predictCompletion', result);
    return result;
  }
}
