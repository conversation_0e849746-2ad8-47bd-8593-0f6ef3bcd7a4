import { BaseWebviewRpc } from '@/utils/webview';
import { AuthStatus } from '@/agent/lsp-client';
import { ChatActionForm } from './model';

export class ChatPanelRpc extends BaseWebviewRpc {

  readonly auth = {
    updated: (auth: AuthStatus) => {
      return this._req('auth/updated', auth);
    }
  };

  readonly chat = {
    action: (form: ChatActionForm) => {
      return this._req('chat/action', form);
    }
  };
}
