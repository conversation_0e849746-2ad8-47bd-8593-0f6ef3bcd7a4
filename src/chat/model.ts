import { Position } from 'vscode';

export enum ChatAction {
  explain = 'explain',
  fix = 'fix',
  codegenDoc = 'doc',
  codegenUnitTest = 'ut',
  refactor = 'refactor'
}

export enum CodeFactoryAction {
  explain = 'code-factory.explainCode',
  fix = 'code-factory.fixCode',
  improve = 'code-factory.improveCode'
}

export type ChatActionForm = {
  id?: string;
  action: ChatAction;
  message: ChatActionMessage;
  language: string;
};

export type ChatActionMessage = {
  selection?: string;
  file?: string;
  position?: Position;
};

export type ReceivedMessage = {
  type: string;
  data: any;
};

export interface ActionType {
  id?: string;
  type?: string;
}
