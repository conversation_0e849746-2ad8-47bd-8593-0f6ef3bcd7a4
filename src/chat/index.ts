import * as vscode from 'vscode';
import { commands, TextEditor } from 'vscode';
import { agent } from '@/agent';
import {
  ChatAction,
  ChatActionMessage,
  ActionType,
  CodeFactoryAction
} from './model';
import { DEFAULT_CHAT_PAGE_URL, getChatConf, PLUGIN_LABEL } from '@/config';
import { downloadToString } from '@/utils/download';
import { getCollectionName, getOrigin } from '@/utils';
import { EventForm, telemetry } from '@/telemetry';
import { RpcProtocol } from '@/utils/rpc';
import { BaseWebviewPanel, webviewViewToContainer } from '@/utils/webview';
import { ChatPanelRpc } from './rpc';
import { isAuthorized, MENU_STATE } from '@/menu';
import { notify } from '@/notify';
import {
  getVscodeRange,
} from '@/testing/utils';

const VIEW_ID = 'li.codebuddy.view.chat';

let CHAT: ChatPanel | undefined;

export function chat(): ChatPanel {
  if (!CHAT) {
    throw new Error(`[${PLUGIN_LABEL}] Chat panel is not initialized`);
  }
  return CHAT;
}

export async function initChatPanel(
  _context: vscode.ExtensionContext
): Promise<vscode.Disposable[]> {
  const chat = new ChatPanel();
  CHAT = chat;
  return [
    chat,
    vscode.window.registerWebviewViewProvider(VIEW_ID, chat, {
      webviewOptions: { retainContextWhenHidden: true }
    })
  ];
}

async function buildActionForm(
  editor: TextEditor,
  action: ChatAction,
  range?: vscode.Range,
  id?: string
) {
  const doc = editor.document;
  const file = doc.uri.toString();
  const language = doc.languageId;
  const selection = doc.getText(range || editor.selection);
  const position = editor.selection.active;

  let message: ChatActionMessage;
  if (selection) {
    message = {
      selection,
    };
  } else {
    message = {
      file,
      position,
    };
  }

  return {
    id,
    action,
    language,
    message
  };
}

export class ChatPanel extends BaseWebviewPanel<ChatPanelRpc>
  implements vscode.WebviewViewProvider {

  constructor() {
    super();
  }

  public async resolveWebviewView(
    webviewView: vscode.WebviewView,
    _context: vscode.WebviewViewResolveContext<unknown>,
    _token: vscode.CancellationToken
  ) {
    await this._resetWebview(webviewViewToContainer(webviewView));
  }

  protected async _resolvePageHtml() {
    // beta 使用, 不声明也可以获取到 config
    const conf = getChatConf();
    const url = conf.get('url', DEFAULT_CHAT_PAGE_URL);
    const html = await downloadToString(url);
    telemetry().offer({
      name: 'view:sidebar.chat.page-loaded',
      details: {
        url
      }
    });
    return html;
  }

  public async focus() {
    commands.executeCommand(`${VIEW_ID}.focus`);
  }

  protected async _createApi(protocol: RpcProtocol) {
    return new ChatPanelRpc(protocol);
  }

  protected async _setup() {
    const rpc = this.rpc;
    rpc.register('telemetry', async (form: EventForm) => {
      telemetry().offer(form);
      return true;
    });

    // deprecated
    rpc.register('collection/get', async (): Promise<string | undefined> => {
      return getCollectionName();
    });

    rpc.register('get/origin', async (): Promise<string | undefined> => {
      return getOrigin();
    });

    rpc.register('prompt/snippets', async (form: any) => {
      return await agent().client.sendRequest('codebuddy/extra/prompt/snippets', form);
    });

    // TODO: 删除此行
    rpc.register('chat/action', async (type: ActionType) => {
      this.triggerAction(type.type as ChatAction, {}, type.id);
    });

    agent().on('auth:updated', () => {
      this.updateAuthIfReady();
    });

    this._ready$.subscribe(ready => {
      if (!ready) {
        return;
      }
      this.updateAuthForce();
      telemetry().offer({ name: 'view:sidebar.chat.ready' });
    });
  }

  async updateAuthIfReady() {
    if (!this.ready) {
      return;
    }
    this.updateAuthForce();
  }

  private async updateAuthForce() {
    this.rpc.auth.updated(MENU_STATE.value.auth);
  }

  async triggerAction(action: ChatAction, obj: Object, id?: string) {
    if (!isAuthorized()) {
      notify.requireLogin();
      return;
    }

    let editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }
    // Async
    this.focus();
    const range = getVscodeRange(obj);
    const form = await buildActionForm(editor, action, range, id);

    this.readyAndRun(async () => this.rpc.chat.action(form));
  }

  async triggerCodeFactoryAction(action: CodeFactoryAction, obj: Object) {
    if (!isAuthorized()) {
      notify.requireLogin();
      return;
    }

    let editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }
    vscode.commands.executeCommand(action, obj, { 'codebuddy': true });
  }
}
