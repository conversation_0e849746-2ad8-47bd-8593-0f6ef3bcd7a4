import { commands, window } from 'vscode';
import {
  CMD_AGENT_REINSTALL_FORCE,
  CMD_AGENT_UPGRADE_TO_VERSION,
  CMD_AUTH_LOGIN,
  CMD_AUTH_LOGOUT_CONFIRMED,
  CMD_OPEN_SETTINGS,
  CMD_TOGGLE_ENABLED
} from './commands';
import { COMP_CODE_PLUGIN, COMPONENT_CONFIG, PLUGIN_LABEL } from './config';
import { persistentEvent } from './telemetry';
import { CMD_PLUGIN_UPGRADE_TO_VERSION } from '@/upgrade/upgrade';
import { Component } from '@/upgrade/check';
import { appChannel, appVersion } from '@/extension';
import { agentVersion } from './agent';
import { isAuthorized, MENU_STATE } from '@/menu';
import { AuthStatus } from '@/agent/lsp-client';

export type NotifyType = 'info' | 'warning' | 'error';

type Actions = { [key: string]: (key: string) => void };

const NOOP = () => {
};

function info(code: string, message: string, actions: Actions) {
  showNotify('info', code, message, actions);
}

function warning(code: string, message: string, actions: Actions) {
  showNotify('warning', code, message, actions);
}

function error(code: string, message: string, actions: Actions) {
  showNotify('error', code, message, actions);
}

function showNotify(
  type: NotifyType,
  code: string,
  message: string,
  actions: Actions
) {
  const event = persistentEvent({
    name: 'view:notify-' + code
  });

  let show;
  switch (type) {
    case 'info':
      show = window.showInformationMessage;
      break;
    case 'warning':
      show = window.showWarningMessage;
      break;
    case 'error':
      show = window.showErrorMessage;
      break;
    default:
      throw new Error(`Unknown notify type: ${type}`);
  }

  message = `[${PLUGIN_LABEL}] ${message}`;
  show(message, ...Object.keys(actions)).then(
    selection => {
      event.end({
        details: {
          selection
        }
      });
      if (selection) {
        const action = actions[selection];
        if (action) {
          action(selection);
        }
      }
    },
    reason => {
      event.end({
        details: {
          rejected: reason
        }
      });
    }
  );
}

/* eslint-disable @typescript-eslint/naming-convention */
const settingsAction = {
  ' 设置 ': () => {
    commands.executeCommand(CMD_OPEN_SETTINGS);
  }
};

export const notify = {
  info,
  warning,
  error,
  showNotify,

  whenUpgrading(component: Component) {
    info(
      'upgrading',
      `正在升级 ${COMPONENT_CONFIG[component].name}...`,
      {
        ' OK ': NOOP
      }
    );
  },

  requireLogin() {
    info(
      'auth.required',
      '当前未登录，此功能需要登录后使用.',
      {
        ' 登录... ': () => {
          commands.executeCommand(CMD_AUTH_LOGIN);
        },
        ' 暂不 ': NOOP
      }
    );
  },

  whenAgentStartFailed() {
    error('agent.startup.failed', '内核启动失败', {
      ...settingsAction,
      ' 重新安装 ': () => {
        commands.executeCommand(CMD_AGENT_REINSTALL_FORCE);
      },
      ' 禁用 ': () => {
        commands.executeCommand(CMD_TOGGLE_ENABLED);
      }
    });
  },


  whenDisconnected() {
    error('plugin.disconnected', '已断开连接 ', {
      ...settingsAction
    });
  },

  confirmLoginCallback() {
    info('auth.page-opened',
      '认证页面已打开，请允许跳转，并浏览器中完成登录', {
        ' OK ': NOOP
      });
  },

  whenMainStatusClickedMultiTimes() {
    const actions: Actions = {
      ' OK ': NOOP,
    };

    if (!isAuthorized()) {
      actions[' 登录... '] = () => {
        commands.executeCommand(CMD_AUTH_LOGIN);
      };
    }

    info('hint.main-status.clicked-multi-times',
      '不要再点啦，主菜单已经在上方弹出啦~',
      actions);
  },

  whenLoginFailed(msg?: string) {
    error('auth.failed', msg ? `认证失败, 错误详情: ${msg}` : '认证失败', {
      ' OK ': NOOP
    });
  },

  whenLogout() {
    info('auth.logout.success', '已退出登录', {
      ' OK ': NOOP
    });
  },

  whenLogin(auth: AuthStatus) {
    const account = auth.account;
    info('auth.login.success',
      `登录成功：${account?.nickname || '--'} / ${account?.email || '--'}`,
      {
        ' OK ': NOOP
      });
  },

  confirmLogout() {
    const auth = MENU_STATE.value.auth;
    if (!auth.authorized) {
      showNotify('warning', 'auth.logout.no-authorized', '当前未登录，无需注销', {
        ' OK ': NOOP
      });
    }

    const account = auth.account;
    info('auth.logout.confirm',
      `确定退出账号: ${account?.nickname || '--'} (${account?.email || '--'})?`,
      {
        ' 退出登录 ': () => {
          commands.executeCommand(CMD_AUTH_LOGOUT_CONFIRMED);
        },
        ' 取消 ': NOOP
      });
  },

  whenNoUpgrade(component: Component, version: string) {
    info(
      'no-upgrade',
      `当前 ${COMPONENT_CONFIG[component].name} 已经是最新版本 ${version}`,
      {
        ' OK ': NOOP
      }
    );
  },

  whenPluginUpgraded(component: string, version: string) {
    info(
      'plugin.upgraded',
      `${component} 已经更新到 ${version}  重新加载后生效`,
      {
        ' 重新加载 ': () => {
          commands.executeCommand('workbench.action.reloadWindow');
        }
      }
    );
  },

  whenAgentUpgraded(version: string) {
    info(
      'agent.upgraded',
      `内核已经更新到 ${version}`,
      {
        ' OK ': NOOP
      }
    );
  },

  whenAgentUpgradeFailed(version: string) {
    info(
      'agent.upgrade-failed',
      `内核 ${version} 更新失败, 请联系开发组`,
      {
        ' 重新安装 ': () => {
          commands.executeCommand(CMD_AGENT_REINSTALL_FORCE);
        },
        OK: () => {
        }
      }
    );
  },

  whenPluginUpgradeFailed(component: string, version: string) {
    info(
      'plugin.upgrade-failed',
      `${component} 插件 ${version} 更新失败, 请联系开发组`,
      {
        ' OK ': NOOP
      }
    );
  },

  whenUpgradeAvailable(component: Component, version: string) {
    const [name, cmd] = component === 'agent'
      ? ['内核', CMD_AGENT_UPGRADE_TO_VERSION]
      : ['插件', CMD_PLUGIN_UPGRADE_TO_VERSION];
    info(
      'plugin.upgrade-available',
      `[${name}] 发现新版本: ${version}`,
      {
        ' 立即更新 ': () => {
          commands.executeCommand(cmd, version);
        },
        ' 稍后再说 ': NOOP
      }
    );
  },

  showPluginDetails() {
    info(
      'plugin.details',
      `插件: ${appVersion()},  内核: ${agentVersion()},  更新通道: ${appChannel()}`,
      {
        ' OK ': NOOP
      }
    );
  },

  getRefreshTokenError() {
    error(
      'refresh token error',
      '用户登录状态过期，请重新登录',
      {
        ' OK ': NOOP
      }
    );
  },

  loginSuccess(nickname?: string, email?: string) {
    info('auth.login.success',
      `登录成功：${nickname || '--'}(${email})`,
      {
        ' OK ': NOOP
      });
  },

};
