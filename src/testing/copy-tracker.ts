import { distance } from 'fastest-levenshtein';
import { telemetry } from '@/telemetry';
import {
    CopyForm
} from './rpc';


interface EventEntry {
    startTime: number;
    cmplId: string;
    requestId: string;
    content: string;
    testingPanelId: string;
    pastedContent?: string;
    distance?: number;
}

export interface TestingCopyTrackerEntry {
    copyForm: CopyForm;
}

export class TestingCopyTracker {
    private waitTime: number;
    private events: EventEntry[];
    private timeoutId: NodeJS.Timeout | undefined;
    private acceptionMaxDistance: number;

    public constructor(waitTime: number) {
        this.waitTime = waitTime;
        this.events = [];
        this.timeoutId = undefined;
        this.acceptionMaxDistance = 100;
    }

    private clearTimeout() {
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
            this.timeoutId = undefined;
        }
    }

    public judgeAccepted(content: string) {
        const filteredEvent = this.events.filter(e => {
            const d = distance(content, e.content);
            if (d < this.acceptionMaxDistance) {
                e.distance = d;
                e.pastedContent = content;
                return true;
            }
            return false;
        });
        if (filteredEvent.length > 0) {
            this.emit(Date.now(), filteredEvent[0], 'accepted');
            this.events = [];
        }
    }

    private emit(nowMillis: number, entry: EventEntry, type: 'accepted' | 'ignore') {
        const details = {
            ...entry,
        };
        telemetry().offer({
            name: 'testing:testcase.copy.' + type,
            time: entry.startTime,
            du: nowMillis - entry.startTime,
            details
        });
    }

    public emitAll() {
        this.timeoutId = undefined;
        const nowMillis = Date.now();
        for (let event of this.events) {
            this.emit(
                nowMillis,
                event,
                'ignore'
            );
        }
        this.events = [];
    }

    public take(entry: TestingCopyTrackerEntry) {
        const startTime = Date.now();
        let isTake = false;
        for (let event of this.events) {
            // 排除重复点击 copy 的场景
            if (entry.copyForm.requestId === event.requestId && event.cmplId === entry.copyForm.cmplId && distance(entry.copyForm.content, event.content) === 0) {
                isTake = true;
            }
        }
        if (!isTake) {
            // 复制事件采集放在这里，避免用户多次点击重复采集
            telemetry().offer({
                name: 'testing:testcase.copy',
                time: startTime,
                du: Date.now() - startTime,
                details: entry.copyForm
            });
            this.events.push({
                cmplId: entry.copyForm.cmplId,
                requestId: entry.copyForm.requestId,
                startTime: startTime,
                content: entry.copyForm.content,
                testingPanelId: entry.copyForm.testingPanelId,
            });
        }
        this.events = this.events.slice(0, 10);
        this.clearTimeout();
        this.timeoutId = setTimeout(() => {
            this.emitAll();
        }, this.waitTime);
    }
}
