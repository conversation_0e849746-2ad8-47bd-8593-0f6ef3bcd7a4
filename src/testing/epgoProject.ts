import * as vscode from 'vscode';
import { randomUUID } from 'crypto';
import * as path from 'path';
import { writeFileSync, unlinkSync } from 'fs';

import { TestingPanelRpc } from '@/testing/rpc';
import { TestingRunForm } from '@/testing/rpc';
import { runTestCommand } from '@/testing/utils';

let _isEPGOProjectData: boolean | null = null;

const fileToDetectEPGOProject = 'cmd/portalv2/main.go';
const epGoCompileTestCommandPrefix = 'go test -c ';


export async function isEPGOProject(): Promise<boolean> {
    if (_isEPGOProjectData !== null) {
        return !!_isEPGOProjectData;
    }
    if (!vscode.workspace.workspaceFolders) {
        _isEPGOProjectData = false;
        return false;
    }
    console.log('debug info: workspaceFolders', vscode.workspace.workspaceFolders);
    for (const f of vscode.workspace.workspaceFolders) {
        const files = await vscode.workspace.findFiles(
            new vscode.RelativePattern(f, '**/' + fileToDetectEPGOProject),
            null,
            1
        );
        if (files.length > 0) {
            _isEPGOProjectData = true;
            return true;
        }
    }
    _isEPGOProjectData = false;
    return false;
}

export async function runEPGoProjectTestCase(form: TestingRunForm, rpc: TestingPanelRpc) {
    if (
        vscode.workspace.workspaceFolders &&
        vscode.workspace.workspaceFolders.length > 0
    ) {
        rpc.testing.compileStart();
        const workspacePath = vscode.workspace.workspaceFolders[0].uri.fsPath;
        const focalMethodFileUri = vscode.Uri.file(form.focalMethodFileName);

        const focalMethodRelativeFileName = path.relative(workspacePath, focalMethodFileUri.fsPath);
        const focalMethodRelativeDirname = path.dirname(focalMethodRelativeFileName);
        const focalMethodDirname = path.dirname(focalMethodFileUri.fsPath);

        const tmpID = randomUUID();
        const testFileName = path.join(focalMethodDirname, `${tmpID}_test.go`);
        writeFileSync(testFileName, form.testCaseContent, 'utf8');
        const result = await runTestCommand(`${epGoCompileTestCommandPrefix} ./${focalMethodRelativeDirname}`);
        disposeChange(testFileName);
        rpc.testing.compileDone();
        rpc.testing.compileRepair({
            returncode: result.getReturncode(),
            stderr: result.getStderr(),
            stdout: result.getStdout(),
        });
    }
}


function disposeChange(testFileName: string) {
    unlinkSync(testFileName);
}
