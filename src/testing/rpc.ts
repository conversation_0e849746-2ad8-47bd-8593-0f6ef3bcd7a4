import { BaseWebviewRpc } from '@/utils/webview';
import { Position } from 'vscode';

export interface Context {
  code?: string;
  postion?: Position;
  filePath: string;
  referencedFiles?: string[];
}

export interface TestingCompletion {
  requestId: string;
  context: Context;
  language: string;
}

export interface TestingRunForm {
  testCaseContent: string;
  command: string;
  functionHeader: string;
  focalMethodFileName: string;
}

export interface CopyForm {
  testingPanelId: string;
  content: string;
  requestId: string;
  cmplId: string;
}

export interface FileContent {
  language?: string;
  content?: string;
  filePath?: string;
}

export interface InitInfoForm {
  projectAliasName: string;
  testingPanelId: string;
  focalMethodFileName: string; // 被测函数所在文件，避免生成测试用例之后，用户切换当前激活文件的情况
  workspacePath?: string;
  model?: string;
  isRedZone?: boolean;
}

export interface FileContent {
  language?: string;
  content?: string;
  filePath?: string;
}
export class TestingPanelRpc extends BaseWebviewRpc {
  // TODO: rpc communication with testing webview 给 webview 发送消息的方法
  readonly testing = {
    show: (form: TestingCompletion): Promise<void> => {
      return this._req('testing/show', form);
    },
    processing: (): Promise<void> => {
      return this._req('testing/processing');
    },
    initInfo: (form: InitInfoForm): Promise<void> => {
      return this._req('testing/init/info', form);
    },
    compileDone: (): Promise<void> => {
      return this._req('testing/compile/done');
    },
    compileStart: (): Promise<void> => {
      return this._req('testing/compile/start');
    },
    compileRepair: (form: any): Promise<void> => {
      return this._req('testing/compile/repair', form);
    }
  };
}
