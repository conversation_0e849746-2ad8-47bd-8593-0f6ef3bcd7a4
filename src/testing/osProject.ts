import * as vscode from 'vscode';
import { join } from 'path';
import { readFileSync, writeFileSync, unlinkSync } from 'fs';

import { TestingRunForm, TestingPanelRpc } from '@/testing/rpc';
import { runTestCommand } from '@/testing/utils';

const osTestCaseFilePath =
  'Test/CUnit/Testcase/OS_CUnit/Cicd/common/src/TestCase_LLM_stub.c';
const osTestCaseUnitFilePath =
  'Test/CUnit/Testcase/OS_CUnit/Cicd/common/src/Cicd_common_test.c';
const osTestHeaderFilePath =
  'Test/CUnit/Testcase/OS_CUnit/Cicd/common/inc/LLM_stub_function.h';
const fileToDetectOsProject = 'Test/CUnit/Testcase/OS_CUnit/OS_CUnit.mk';
const osRunTestCommand =
  'export TSK_LICENSE_PATH=/opt/TriCore/ctc/ && cd Applications/TC389_Demo/ && make codegen_bsw CODEGEN_MODE=--debug && make --output-sync -j16 all && cd -';

function genTestCaseHeader(header: string): string {
  // const insertTestCaseHeader = `
  // extern void TestCase_OS_LLM_STUB_50541e2a_23d1_11ef_9343_2e7bd4c7cc62(void);
  // `;
  return header;
}

function extractFunctionName(cFunctionSignature: string): string[] {
  const regex = /\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g;
  return Array.from(cFunctionSignature.matchAll(regex), match => match[1]);
}

function genTestCaseUnit(header: string): string {
  // const insertTestCaseUnit = `
  //     // This is being injected by CodeBuddy, Begin.
  //     {"test_os_llm_stub_50541e2a_23d1_11ef_9343_2e7bd4c7cc62", TestCase_OS_LLM_STUB_50541e2a_23d1_11ef_9343_2e7bd4c7cc62, CUNIT_CASE_P0},
  //     // This is being injected by CodeBuddy, End.
  //     CU_TEST_INFO_NULL
  // `;
  const functionNames = extractFunctionName(header);
  const cases = [] as string[];
  functionNames.forEach(functionName => {
    cases.push(`{"${functionName}_llm", ${functionName}, CUNIT_CASE_P0},`);
  });
  return `
// This is being injected by CodeBuddy, Begin.
${cases.join('\n')}
// This is being injected by CodeBuddy, End.
CU_TEST_INFO_NULL
`;
}

let _isOsProjectData: boolean | null = null;

export async function isOsProject(): Promise<boolean> {
  if (_isOsProjectData !== null) {
    return !!_isOsProjectData;
  }
  if (!vscode.workspace.workspaceFolders) {
    _isOsProjectData = false;
    return false;
  }
  for (const f of vscode.workspace.workspaceFolders) {
    const files = await vscode.workspace.findFiles(
      new vscode.RelativePattern(f, '**/' + fileToDetectOsProject),
      null,
      1
    );
    if (files.length > 0) {
      _isOsProjectData = true;
      return true;
    }
  }
  _isOsProjectData = false;
  return false;
}

export async function runOsProjectTestCase(form: TestingRunForm, rpc: TestingPanelRpc) {
  if (
    vscode.workspace.workspaceFolders &&
    vscode.workspace.workspaceFolders.length > 0
  ) {
    rpc.testing.compileStart();
    const workspacePath = vscode.workspace.workspaceFolders[0].uri.fsPath;
    createTestCaseFile(workspacePath, form.testCaseContent);
    createTestCaseHeaderFile(
      workspacePath,
      genTestCaseHeader(form.functionHeader)
    );
    const insertTestCaseUnit = genTestCaseUnit(form.functionHeader);
    if (!insertTestCaseUnit) {
      vscode.window.showErrorMessage('编译单元测试失败：准备阶段 insertTestCaseUnit');
      return;
    }
    const oldTestCaseUnit = keepOldAndRegisterTestCase(
      workspacePath,
      insertTestCaseUnit
    );
    if (!oldTestCaseUnit) {
      vscode.window.showErrorMessage('编译单元测试失败：准备阶段 oldTestCaseUnit');
      return;
    }
    const result = await runTestCommand(osRunTestCommand);
    disposeChange(workspacePath, oldTestCaseUnit);
    rpc.testing.compileDone();
    const stdout = result.getStdout();
    const stderr = result.getStderr();
    const returncode = result.getReturncode();
    rpc.testing.compileRepair({
      returncode: returncode,
      stderr: stderr,
      stdout: stdout,
    });
  }
}

function keepOldAndRegisterTestCase(
  workspacePath: string,
  testCaseUnit: string
): string {
  const filePath = join(workspacePath, osTestCaseUnitFilePath);
  const content = readFileSync(filePath, 'utf8');
  let c = '#include "LLM_stub_function.h"' + '\n' + content;
  if (!c.includes('CU_TEST_INFO_NULL')) {
    // 此处后面重构
    return '';
  }
  c = c.replace('CU_TEST_INFO_NULL', testCaseUnit);
  writeFileSync(filePath, c, 'utf8');
  return content;
}

function genTestCaseHeaderFileContent(headerContent: string): string {
  return `
#ifndef TEST_CICD_LLM_STUB_FUNCTION_H_
#define TEST_CICD_LLM_STUB_FUNCTION_H_

#ifdef __cplusplus
extern "C"{
#endif

${headerContent}

#ifdef __cplusplus
}
#endif

#endif
`;
}

function createTestCaseHeaderFile(
  workspacePath: string,
  headerContent: string
) {
  const content = genTestCaseHeaderFileContent(headerContent);
  writeFileSync(join(workspacePath, osTestHeaderFilePath), content, 'utf8');
}

function createTestCaseFile(workspacePath: string, testCaseContent: string) {
  writeFileSync(
    join(workspacePath, osTestCaseFilePath),
    testCaseContent,
    'utf8'
  );
}


function disposeChange(workspacePath: string, oldTestCaseUnit: string) {
  unlinkSync(join(workspacePath, osTestCaseFilePath));
  unlinkSync(join(workspacePath, osTestHeaderFilePath));
  writeFileSync(
    join(workspacePath, osTestCaseUnitFilePath),
    oldTestCaseUnit,
    'utf8'
  );
}
