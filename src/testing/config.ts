import * as yaml from 'yaml';
import * as fs from 'fs';
import * as vscode from 'vscode';
import { TESTING_CONFIG_FILENME } from '@/config';
import { mergeIfAbsent } from '@/utils';
import deepEqual from 'deep-equal';

export interface Config {
  languageConfigs: Partial<{ [key: string]: LanguageConfig }>;
  numDesiredTests: Partial<Number>;
  testing?: Partial<{ [key: string]: any; }>;
}

interface LanguageConfig {
  framework: Partial<string>;
  exampleTest: Partial<string>;
  testRunCmd: Partial<string>;
  testStyle: Partial<string>;
}

const DEFAULET_TESTING_CONFIG: Config = {
  languageConfigs: {},
  numDesiredTests: 3,
  testing: {}
};
export let testingConfig: Config = { ...DEFAULET_TESTING_CONFIG };

const workspaceFolder =
  vscode.workspace.workspaceFolders?.length === 1
    ? vscode.workspace.workspaceFolders[0]
    : null;
const configFilePath = `${workspaceFolder?.uri.fsPath}/${TESTING_CONFIG_FILENME}`;

export function initTestingConfig(): void {
  let c: Config | undefined;
  try {
    fs.accessSync(configFilePath, fs.constants.F_OK | fs.constants.R_OK);
    const fileContent = fs.readFileSync(configFilePath, 'utf8');
    c = yaml.parse(fileContent);
  } catch {
    console.log('config file not exist');
  }
  mergeIfAbsent(testingConfig, c || {});
}

export function updateTestingConfig(config: Config): Config {
  const c = Object.assign(testingConfig, config);
  saveTestingConfig();
  initTestingConfig();
  return c;
}

export function getTestingConfig(): Config {
  return testingConfig;
}

export function saveTestingConfig(): void {
  console.log('save test config');
  console.log(testingConfig, 'test config');
  console.log(DEFAULET_TESTING_CONFIG, 'default config');
  if (deepEqual(testingConfig, DEFAULET_TESTING_CONFIG)) {
    console.log('config is default, dont need save');
    return;
  }
  const configData = yaml.stringify(testingConfig);
  try {
    fs.writeFileSync(configFilePath, configData);
    console.log(`config file ${configFilePath}  write success`);
  } catch {
    console.log('config file write failed');
  }
}
