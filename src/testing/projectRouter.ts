// 识别当前打开的项目是哪个项目
// 后期计划把这个迁移到单测生成的配置里面，默认配置项目的标识
import { isOsProject } from './osProject';
import { isEPGOProject } from './epgoProject';

export const projectAliasNameVCOS = 'project_vcos';
export const projectAliasNameDEFAULT = 'project_default';
export const projectAliasNameEPGO = 'project_epgo';

export async function getprojectAliasName(): Promise<string> {
    if (await isEPGOProject()) {
        return projectAliasNameEPGO;
    }
    if (await isOsProject()) {
        return projectAliasNameVCOS;
    }
    return projectAliasNameDEFAULT;
}
