import * as vscode from 'vscode';
import * as path from 'path';
import {
    CodeLensCommandContext,
} from '@/codelens/index';
import { executeBuildTask, CustomTaskTerminal } from '@/utils/vscode/task';

export function genTestingPanelTitle(obj: Object): string {
    const editor = vscode.window.activeTextEditor;
    let fileName = '';
    if (editor) {
        fileName = editor.document.fileName;
        fileName = path.basename(fileName);
    }
    let symbolName = 'code snippet';
    if (obj.hasOwnProperty('symbolName')) {
        const symbol = obj as CodeLensCommandContext;
        symbolName = symbol.symbolName;
    }
    return `CodeBuddy - ${fileName}[${symbolName}]`;
}

export function getVscodeRange(obj: Object): vscode.Range | undefined {
    let range = undefined;
    if (obj.hasOwnProperty('range')) {
        const symbol = obj as CodeLensCommandContext;
        range = symbol.range;
    }
    return range;
}

export async function runTestCommand(command: string) {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error('没有打开任何工作区');
    }
    const workspaceFolder = workspaceFolders[0];
    const cwd = workspaceFolder.uri.fsPath;

    const terminal = new CustomTaskTerminal(command, cwd);
    const customExecution = new vscode.CustomExecution(async (): Promise<vscode.Pseudoterminal> => {
        return terminal;
    });

    const task = new vscode.Task(
        { type: 'shell' },
        workspaceFolder,
        'compilation task',
        'Codebuddy',
        customExecution
    );

    await executeBuildTask(task);

    // console.log(terminal.getReturncode(), terminal.getStdout(), terminal.getStderr());
    return terminal;
}
