import * as vscode from 'vscode';
import * as fs from 'fs';
import {
  CMD,
  DEFAULT_TESTING_PANEL_PAGE_URL,
  getTestingConf
} from '@/config';
import { BaseWebviewPanel, webviewPanelToContainer } from '@/utils/webview';
import { RpcProtocol } from '@/utils/rpc';
import {
  TestingPanelRpc,
  TestingCompletion,
  Context,
  FileContent,
  TestingRunForm,
  CopyForm
} from './rpc';
import { TestingCopyTracker } from './copy-tracker';
import { telemetry } from '@/telemetry';
import { downloadToString } from '@/utils/download';
import { EventForm } from '@/telemetry';
import { isAuthorized } from '@/menu';
import { notify } from '@/notify';
import { agent } from '@/agent';
import { randomUUID } from 'crypto';
import {
  initTestingConfig,
  saveTestingConfig,
  testingConfig,
  getTestingConfig,
  updateTestingConfig
} from './config';
import {
  genTestingPanelTitle,
  getVscodeRange,
} from '@/testing/utils';
import { runOsProjectTestCase } from './osProject';
import { runEPGoProjectTestCase } from './epgoProject';
import {
  getprojectAliasName,
  projectAliasNameVCOS,
  projectAliasNameEPGO
} from './projectRouter';
import {
  onPasteEventHandlers,
  findFilesInWorkspace,
  getWorkspacePath
} from '@/utils/vscode/workspace';
import { insertAtCursor, readFileContent } from '@/utils/vscode/editor';
import { getActiveFileName } from '@/utils/vscode/editor';
import { IS_RED_ZONE } from '@/judge_env';

const CMD_TEST = `${CMD}.testing`;
export const CMD_TEST_GENERATE = `${CMD_TEST}.generate`;

const TESTING_COPY_TRACKER = new TestingCopyTracker(300000); // 300000ms = 5min
onPasteEventHandlers.push((c: string) => TESTING_COPY_TRACKER.judgeAccepted(c));

let PANELS = [] as TestingPanel[];

function panel(obj: Object): TestingPanel {
  const webview = vscode.window.createWebviewPanel(
    'TestingPanel',
    genTestingPanelTitle(obj),
    vscode.ViewColumn.Two,
    { enableScripts: true, retainContextWhenHidden: true }
  );
  webview.iconPath = vscode.Uri.file('assets/icons/testing.svg');

  const panel = new TestingPanel(webview);
  PANELS.push(panel);
  return panel;
}

export async function initTestingPanel(
  _context: vscode.ExtensionContext
): Promise<vscode.Disposable[]> {
  initTestingConfig();
  return [
    vscode.commands.registerCommand(
      CMD_TEST_GENERATE,
      async (obj) => {
        await panel(obj).generate(obj);
      }
    ),
    { dispose }
  ];
}

function dispose() {
  saveTestingConfig();
  PANELS.forEach((panel: TestingPanel) => panel.dispose());
  PANELS.length = 0;
}

class TestingPanel extends BaseWebviewPanel<TestingPanelRpc> {
  latestEditor?: vscode.TextEditor;
  panelId: string;

  constructor(webviewPanel: vscode.WebviewPanel) {
    super();
    this._resetWebview(webviewPanelToContainer(webviewPanel));
    this.panelId = randomUUID();
  }

  protected async _active() {
    this.show();
  }

  protected async _resolvePageHtml() {
    const conf = getTestingConf();
    const url = conf.get('debug_url', DEFAULT_TESTING_PANEL_PAGE_URL);
    const html = await downloadToString(url);
    return html;
  }

  protected async _createApi(protocol: RpcProtocol) {
    return new TestingPanelRpc(protocol);
  }

  protected async registryApi() {
    const api = this.rpc;
    api.register('testing/telemetry', async (form: EventForm) => {
      telemetry().offer(form);
      return true;
    });
    api.register('prompt/snippets', async (form: any) => {
      return await agent().client.sendRequest(
        'codebuddy/extra/prompt/snippets',
        form
      );
    });
    api.register('testing/config/update', async (config: any) => {
      return updateTestingConfig(config);
    });
    api.register('testing/config/get', async () => {
      return getTestingConfig();
    });
    api.register('testing/command/run', async (form: TestingRunForm) => {
      console.log('Testing run vscode: ', form);
      const projectAliasName = await getprojectAliasName();
      if (projectAliasName === projectAliasNameVCOS) {
        runOsProjectTestCase(form, this.rpc);
        telemetry().offer({//编译结果未收集
          name: 'testing:testcase.compile',
          time: Date.now(),
          details: { 'project': 'vcos', testingPanelId: this.panelId }
        });
      }
      if (projectAliasName === projectAliasNameEPGO) {
        runEPGoProjectTestCase(form, this.rpc);
        telemetry().offer({
          name: 'testing:testcase.compile',
          time: Date.now(),
          details: { 'project': 'epgo', testingPanelId: this.panelId }
        });
      }
    });
    api.register('testing/copy', async (c: CopyForm) => {
      TESTING_COPY_TRACKER.take({ copyForm: c });
    });
    api.register('testing/insert-at-cursor', async (c: string) => {
      insertAtCursor(c);
    });
    api.register('testing/find-files-in-workspace', async (includePattern: string, excludePattern?: string, maxResults?: number) => {
      const ret = await findFilesInWorkspace(includePattern, excludePattern, maxResults);
      return ret;
    });
    api.register('testing/read-file', async (filepath: string) => {
      return readFileContent(filepath);
    });
    api.register('testing/openAsFile', async (data: any) => {
      const fileInfo = data as FileContent;
      console.log(fileInfo, 'fileInfo');
      let doc = await vscode.workspace.openTextDocument(fileInfo);
      if (fileInfo.filePath) {
        try {
          if (fs.existsSync(fileInfo.filePath)) {
            vscode.window.showWarningMessage('文件已经存在！');
            return false;
          }
          fs.writeFileSync(fileInfo.filePath, fileInfo.content || '');
          let newFileUri = vscode.Uri.file(fileInfo.filePath);
          doc = await vscode.workspace.openTextDocument(newFileUri);
        } catch {
          vscode.window.showErrorMessage('创建文件失败！');
          return false;
        }
      }
      await vscode.window.showTextDocument(doc);
      return true;
    });

    api.register('testing/openAsFile', async (data: any) => {
      const fileInfo = data as FileContent;
      console.log(fileInfo, 'fileInfo');
      let doc = await vscode.workspace.openTextDocument(fileInfo);
      if (fileInfo.filePath) {
        try {
          if (fs.existsSync(fileInfo.filePath)) {
            vscode.window.showWarningMessage('文件已经存在！');
            return false;
          }
          fs.writeFileSync(fileInfo.filePath, fileInfo.content || '');
          let newFileUri = vscode.Uri.file(fileInfo.filePath);
          doc = await vscode.workspace.openTextDocument(newFileUri);
        } catch {
          vscode.window.showErrorMessage('创建文件失败！');
          return false;
        }
      }
      await vscode.window.showTextDocument(doc);
      return true;
    });

    api.register('testing/file/insert', async data => {
      // TODO: insert context
      console.log(data, 'data');
    });
  }

  protected async _setup() {
    const sub = this.subscribeReady(async ready => {
      if (ready) {
        sub.unsubscribe();
        const metrics = this.setupMetrics;
        if (!metrics) {
          return;
        }
        const now = Date.now();
        const { startTime, finishedTime, setupWebviewCost } = metrics;
        telemetry().offer({
          name: 'testing:panel.ready',
          time: startTime,
          du: now - startTime,
          details: {
            setupCost: finishedTime - startTime,
            setupWebviewCost,
            testingPanelId: this.panelId
          }
        });
      }
    });
    await this.registryApi();
    const testingConf = getTestingConf();
    this.rpc.testing.initInfo({
      projectAliasName: await getprojectAliasName(),
      testingPanelId: this.panelId,
      focalMethodFileName: getActiveFileName(this.latestEditor)?.path || '',
      workspacePath: getWorkspacePath()?.path,
      model: testingConfig.testing?.modelName || '',
      isRedZone: IS_RED_ZONE,
    });
  }

  private async _generateForm(
    editor: vscode.TextEditor,
    position: vscode.Position,
    range?: vscode.Range
  ) {
    const doc = editor.document;
    const form: TestingCompletion = {
      requestId: randomUUID(),
      context: {
        filePath: editor.document.uri.toString(),
        postion: position,
        code: doc.getText(range || editor.selection)
      } as Context,
      language: doc.languageId
    };
    return form;
  }
  public async generate(obj: Object) {
    if (!isAuthorized()) {
      notify.requireLogin();
      return;
    }

    let editor = vscode.window.activeTextEditor;
    if (!editor) {
      console.log('WARN: No active editor to generate testcases');
      return;
    }
    this.latestEditor = editor;
    this.show();
    const range = getVscodeRange(obj);
    let position; // position 不能获取光标所在位置，光标位置不一定在触发单测的函数内部，需要保证传入 codebuddy/extra/prompt/snippets 接口的 position 在函数体内
    if (range?.start && range?.end) { // 通过 codelens 触发
      position = { line: Math.ceil((range?.start.line + range?.end.line) / 2), character: range?.start.character } as vscode.Position;
    } else { // 选中通过右键菜单触发，这里目前这样处理并不能完全保证position落在函数内部，无法确定用户选择的函数位于选中的什么位置，只能覆盖大部分场景
      position = { line: Math.ceil((editor.selection.start.line + editor.selection.end.line) / 2), character: editor.selection.start.character } as vscode.Position;
    }
    const [_, form] = await Promise.all([
      this.readyAndRun(async () => await this.rpc.testing.processing()),
      this._generateForm(editor!, position, range)
    ]);
    const responsedAt = Date.now();
    this.rpc.testing.show(form);
    agent().emitCompletionEvent({
      requestId: form.requestId,
      code: 'testing',
      time: responsedAt,
      du: Date.now() - responsedAt
    });
  }
}
