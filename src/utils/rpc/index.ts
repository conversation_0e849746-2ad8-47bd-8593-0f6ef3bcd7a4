import { VsWebviewProtocol } from '@/utils/rpc/webview';
import { Webview } from 'vscode';

export interface RpcProtocol {
  register(method: string, handler: <PERSON>pcHand<PERSON>): void;

  request(method: string, params: any[]): Promise<any>;
}

export type RpcHandler = (...params: any) => Promise<any>;

export interface RpcError {
  code?: string;
  message?: string,
}

export function createVsWebviewProtocol(webview: Webview): RpcProtocol {
  return new VsWebviewProtocol(webview);
}
