import { firstV<PERSON><PERSON><PERSON><PERSON>, Subject } from 'rxjs';
import { <PERSON>p<PERSON><PERSON><PERSON><PERSON>, RpcProtocol } from './index';
import { Webview } from 'vscode';

let ID_SEQ: number = 1;

interface RpcMessage {
  type: string;
  data: RpcRequest | RpcResponse;
}

interface RpcLegecyMessage extends RpcMessage {
  state?: string;
}

interface RpcRequest {
  id: number;
  method: string;
  params: any[],
}

interface RpcResponse {
  id: number;
  success: boolean;
  code?: string;
  message?: string,
  data?: any;
}

interface MessagePoster {
  postMessage(message: RpcMessage): void;
}

export class VsWebviewProtocol implements RpcProtocol {

  readonly target: MessagePoster;
  readonly webview: Webview;
  readonly _handlers: { [key: string]: RpcHand<PERSON> } = {};
  readonly _pendingRequests: { [key: number]: Subject<any> } = {};

  constructor(webview: Webview) {
    this.target = webview;
    this.webview = webview;
    this._setup();
  }

  private _setup() {
    this.webview.onDidReceiveMessage(message => {
      this._handleMessage(message as RpcMessage);
    });
  }

  register(method: string, handler: RpcHandler): void {
    this._handlers[method] = handler;
  }

  request(method: string, params: any[]): Promise<any> {
    const id = ID_SEQ++;

    const msg = {
      type: 'rpc',
      data: {
        id,
        method,
        params,
      }
    };

    // console.log('DEBUG: Sending RPC message', msg);
    this.target.postMessage(msg);

    const subject = new Subject();
    this._pendingRequests[id] = subject;
    return firstValueFrom(subject);
  }

  private _handleMessage(message: RpcMessage) {
    if (!message.type) {
      return;
    }
    switch (message.type) {
      case 'rpc':
        this._handleRpc(message.data as RpcRequest);
        break;
      case 'rpc-response':
        this._handleResponse(message.data as RpcResponse);
        break;
      default:
      // Ignore unknown message type
    }
  }

  private _handleRpc(form: RpcRequest) {
    // console.log('Received rpc message', form);
    const handler = this._handlers[form.method];
    if (!handler) {
      console.log('No handler for rpc method', form.method);
      return;
    }
    handler.apply(null, form.params).then(
      data => {
        this.target.postMessage({
          type: 'rpc-response',
          data: {
            id: form.id,
            success: true,
            data,
          }
        });
      },
      error => {
        this.target.postMessage({
          type: 'rpc-response',
          data: {
            id: form.id,
            success: false,
            code: error?.code || 'InternalError',
            message: error?.message || `${error}`,
          }
        });
      }
    );
  }

  private _handleResponse(resp: RpcResponse) {
    const id = resp.id;
    const subject = this._pendingRequests[id];
    if (!subject) {
      console.log('No pending requests for RpcResponse', resp);
      return;
    }
    if (resp.success) {
      subject.next(resp.data);
    } else {
      subject.error(resp);
    }
    subject.complete();
    delete this._pendingRequests[id];
  }
}
