// 利用Map是稳定的插入顺序来构建LRU
export class LRU<K, V> {
  private size: number;
  private map: Map<K, V>;

  constructor(size: number) {
    this.size = size;
    this.map = new Map();
  }

  public add(key: K, value: V) {
    // For size 0 LRU, don't add anything
    if (this.size === 0) {
      return;
    }
    this.map.delete(key);
    this.map.set(key, value);
    this.popOneIfFull();
  }

  public has(key: K) {
    return this.map.has(key);
  }

  public get(key: K) {
    return this.map.get(key);
  }

  private popOneIfFull() {
    if (this.map.size > this.size) {
      // remove the first inserted key if full
      for (const k of this.map.keys()) {
        this.map.delete(k);
        break;
      }
    }
  }
}
