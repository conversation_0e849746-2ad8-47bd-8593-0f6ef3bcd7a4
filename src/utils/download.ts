import * as https from 'https';
import { RequestOptions } from 'https';
import * as http from 'http';
import { ClientRequest, IncomingMessage } from 'http';
import * as fs from 'fs';
import * as url from 'url';

export function downloadToString(urlStr: string): Promise<string> {
  return downloadResource(urlStr, (response, resolve, reject) => {
    let downloadedData = '';
    response.on('data', data => {
      downloadedData += data;
    });
    response.on('error', error => {
      reject(error);
    });
    response.on('end', () => {
      resolve(downloadedData);
    });
  });
}

export function downloadToFile(
  urlStr: string,
  destinationPath: string
): Promise<void> {
  return downloadResource(urlStr, (response, resolve, reject) => {
    const createdFile: fs.WriteStream = fs.createWriteStream(destinationPath);
    createdFile.on('finish', () => {
      resolve();
    });
    response.on('error', error => {
      reject(error);
    });
    response.pipe(createdFile);
  });
}

function downloadResource<T>(
  urlStr: string,
  callback: (
    response: IncomingMessage,
    resolve: (value: T | PromiseLike<T>) => void,
    reject: (error: Error) => void
  ) => void,
  custom?: (req: RequestOptions) => RequestOptions
): Promise<T> {
  return new Promise<T>((resolve, reject) => {
    const parsedUrl = url.parse(urlStr);
    let options: RequestOptions = {
      headers: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'User-Agent': 'Li-CodeBuddy'
      },
      timeout: 30_000
    };

    if (custom) {
      options = custom(options);
    }

    const request: ClientRequest = getHttpClient(parsedUrl).request(
      urlStr,
      options,
      response => {
        if (response.statusCode === 301 || response.statusCode === 302) {
          let redirectUrl: string;
          if (typeof response.headers.location === 'string') {
            redirectUrl = response.headers.location;
          } else {
            if (!response.headers.location || response.headers.location) {
              return reject(new Error('Invalid download location received'));
            }
            [redirectUrl] = response.headers.location as string[];
          }
          return resolve(downloadResource(redirectUrl, callback));
        }
        if (response.statusCode !== 200 && response.statusCode !== 403) {
          return reject(
            new Error(`Failed request statusCode ${response.statusCode || ''}`)
          );
        }
        callback(response, resolve, reject);
        response.on('error', error => {
          reject(error);
        });
        return undefined;
      }
    );
    request.on('error', error => {
      reject(error);
    });
    request.end();
  });
}

function getHttpClient(parsedUrl: url.UrlWithStringQuery) {
  return parsedUrl.protocol === 'https:' ? https : http;
}
