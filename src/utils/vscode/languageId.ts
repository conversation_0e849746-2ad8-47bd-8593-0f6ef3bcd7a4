export const supportedLanguages = [
    'javascript', 'typescript', 'python', 'c', 'cpp', 'java', 'go',
    'rust', 'typescriptreact', 'shellscript', 'groovy', 'jsx-tags',
    'sql', 'php',
];

// const languages = await vscode.languages.getLanguages();
// const allLanguages = [
//     "plaintext",
//     "code-text-binary",
//     "scminput",
//     "Log",
//     "log",
//     "bat",
//     "clojure",
//     "coffeescript",
//     "jsonc",
//     "json",
//     "c",
//     "cpp",
//     "cuda-cpp",
//     "csharp",
//     "css",
//     "dart",
//     "diff",
//     "dockerfile",
//     "ignore",
//     "fsharp",
//     "git-commit",
//     "git-rebase",
//     "go",
//     "groovy",
//     "handlebars",
//     "hlsl",
//     "html",
//     "ini",
//     "properties",
//     "java",
//     "javascriptreact",
//     "javascript",
//     "jsx-tags",
//     "jsonl",
//     "snippets",
//     "julia",
//     "juliamarkdown",
//     "tex",
//     "latex",
//     "bibtex",
//     "cpp_embedded_latex",
//     "markdown_latex_combined",
//     "less",
//     "lua",
//     "makefile",
//     "markdown",
//     "markdown-math",
//     "wat",
//     "objective-c",
//     "objective-cpp",
//     "perl",
//     "raku",
//     "php",
//     "powershell",
//     "prompt",
//     "instructions",
//     "chatmode",
//     "jade",
//     "python",
//     "r",
//     "razor",
//     "restructuredtext",
//     "ruby",
//     "rust",
//     "scss",
//     "search-result",
//     "shaderlab",
//     "shellscript",
//     "sql",
//     "swift",
//     "typescript",
//     "typescriptreact",
//     "vb",
//     "xml",
//     "xsl",
//     "dockercompose",
//     "yaml",
//     "go.mod",
//     "go.work",
//     "go.sum",
//     "gotmpl",
//     "jsonnet",
//     "env",
//     "ra_syntax_tree",
//     "toml",
//     "lldb.disassembly"
// ];
