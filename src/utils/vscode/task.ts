import * as vscode from 'vscode';
import * as childProcess from 'child_process';

export class CustomTaskTerminal implements vscode.Pseudoterminal {
    private writeEmitter = new vscode.EventEmitter<string>();
    public onDidWrite: vscode.Event<string> = this.writeEmitter.event;

    private closeEmitter = new vscode.EventEmitter<void>();
    public onDidClose?: vscode.Event<void> = this.closeEmitter.event;

    private process?: childProcess.ChildProcess;
    private returncode: number = 0;
    private stderr: string = '';
    private stdout: string = '';

    constructor(private command: string, private cwd: string) { }

    open(_initialDimensions: vscode.TerminalDimensions | undefined): void {
        const commandMessage = `> Executing task in folder ${this.cwd}: ${this.command}\n\n`;
        this.writeEmitter.fire(commandMessage);

        // 使用 child_process 执行命令
        this.process = childProcess.exec(this.command, { cwd: this.cwd });

        // 监听 stdout 数据
        this.process.stdout?.on('data', (data: string) => {
            this.stdout += data;
            this.writeEmitter.fire(data);
        });

        // 监听 stderr 数据
        this.process.stderr?.on('data', (data: string) => {
            this.stderr += data;
            this.writeEmitter.fire(data);
        });

        // 监听进程结束
        this.process.on('close', (code: number) => {
            this.returncode = code;
            this.writeEmitter.fire(`\r\n进程退出，状态码：${code}\r\n`);
            this.closeEmitter.fire();
        });
    }

    close(): void {
        // 当终端被用户关闭时调用
        if (this.process) {
            this.process.kill();
            this.returncode = -1;
        }
    }

    getReturncode(): number {
        return this.returncode;
    }

    getStderr(): string {
        return this.stderr;
    }

    getStdout(): string {
        return this.stdout;
    }
}

export async function executeBuildTask(task: vscode.Task) {
    const execution = await vscode.tasks.executeTask(task);

    return new Promise<void>(resolve => {
        let disposable = vscode.tasks.onDidEndTask((e) => {
            if (e.execution === execution) {
                disposable.dispose();
                resolve();
            }
        });
    });
}
