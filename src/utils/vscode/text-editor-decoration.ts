import * as vscode from 'vscode';
import { diff_match_patch } from 'diff-match-patch';
import '@/utils/enhance-diff-match-patch';

import { getControlKey } from '@/utils/os';

const diffDecorationType = vscode.window.createTextEditorDecorationType({});
const originDecorationType = vscode.window.createTextEditorDecorationType({
    // backgroundColor: 'rgba(45, 134, 222, 0.38)',
});

export function cleanCodeDiffDecorations(activeEditor: vscode.TextEditor) {
    activeEditor.setDecorations(diffDecorationType, []);
    activeEditor.setDecorations(originDecorationType, []);
}

export function getHtmlOfDiff(oldText: string, newText: string): string {
    const dmp = new diff_match_patch();
    // @ts-ignore
    const diffs = dmp.diff_wordMode(oldText, newText);
    dmp.diff_cleanupSemantic(diffs);
    const html = dmp.diff_prettyHtml(diffs);
    return '<div>' + html + '</div>';
}

export function decorateCodeDiff(activeEditor: vscode.TextEditor, codeRange: vscode.Range, oldText: string, newText: string, summary: string) {
    if (!activeEditor) {
        return;
    }
    cleanCodeDiffDecorations(activeEditor);
    const maxColumn = getMaxColumnInRange(activeEditor.document, codeRange);
    const diffDecorations: vscode.DecorationOptions[] = [];
    // https://github.com/microsoft/vscode/blob/6d2920473c6f13759c978dd89104c4270a83422d/src/vs/base/browser/markdownRenderer.ts#L296
    const hoverMessage = new vscode.MarkdownString();
    hoverMessage.supportHtml = true;
    hoverMessage.isTrusted = true;
    hoverMessage.supportThemeIcons = true;
    hoverMessage.appendMarkdown(summary);
    hoverMessage.appendMarkdown('\n\n');

    const diffHtml = getHtmlOfDiff(ensureNewline(oldText || ''), ensureNewline(newText || ''));
    console.log('diffHtml:', diffHtml);
    let diffHtmlForMarkdown = diffHtml.replaceAll('background', 'background-color')
        .replaceAll('del', 'span')
        .replaceAll('ins', 'span')
        .replaceAll('&para;', '');
    if (vscode.window.activeColorTheme.kind === vscode.ColorThemeKind.Light) {
        diffHtmlForMarkdown = diffHtmlForMarkdown.replaceAll('#ffe6e6', '#FBA2A9').replaceAll('#e6ffe6', '#D7E8B1');;
    } else {
        diffHtmlForMarkdown = diffHtmlForMarkdown.replaceAll('#ffe6e6', '#730F21').replaceAll('#e6ffe6', '#46692D');
    }
    hoverMessage.appendMarkdown(diffHtmlForMarkdown);

    diffDecorations.push({
        range: new vscode.Range(
            new vscode.Position(codeRange.start.line, maxColumn + 1),
            new vscode.Position(codeRange.start.line, maxColumn + 1)
        ),
        hoverMessage: hoverMessage,
        renderOptions: {
            dark: {
                after: {
                    contentText: '【' + getControlKey() + ';】' + summary,
                    color: '#000000',
                    margin: '0 0 0 10px',
                    backgroundColor: '#E0E0E0',    // 浅灰色
                },
            },
            light: {
                after: {
                    contentText: '【' + getControlKey() + ';】' + summary,
                    color: '#FFFFFF',
                    margin: '0 0 0 10px',
                    backgroundColor: 'rgb(105,105,105)',
                },
            }
        }
    });
    const origins: vscode.DecorationOptions[] = [{
        range: codeRange,
    }];
    activeEditor.setDecorations(originDecorationType, origins);
    activeEditor.setDecorations(diffDecorationType, diffDecorations);
}

export function getMaxColumnInRange(document: vscode.TextDocument, range: vscode.Range): number {
    if (range.isEmpty) {
        return range.start.character;
    }
    let maxColumn = 0;
    for (let i = range.start.line; i <= range.end.line; i++) {
        const line = document.lineAt(i);
        let currentLineEffectiveEndColumn = line.range.end.character;
        if (currentLineEffectiveEndColumn > maxColumn) {
            maxColumn = currentLineEffectiveEndColumn;
        }
    }
    return maxColumn;
}

// 在创建 patch 之前，确保文本末尾有换行符
function ensureNewline(text: string) {
    return text.endsWith('\n') ? text : text + '\n';
}
