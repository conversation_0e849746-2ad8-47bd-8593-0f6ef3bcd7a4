import * as vscode from 'vscode';

import { readFileSync } from 'fs';


export function getActiveFileName(editor?: vscode.TextEditor): vscode.Uri | undefined {
    if (!editor) {
        editor = vscode.window.activeTextEditor;
    }
    return editor?.document.uri;
}

export function insertAtCursor(c: string) {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showInformationMessage('不存在活动的编辑器，插入代码失败');
        return;
    }
    const selections = editor.selections;
    editor.edit(editBuilder => {
        selections.forEach(selection => {
            editBuilder.insert(selection.active, c);
        });
    });
}

export function readFileContent(filepath: string): string {
    const f = vscode.Uri.file(filepath);
    const content = readFileSync(f.fsPath, 'utf8');
    return content;
}
