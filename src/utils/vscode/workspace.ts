import * as vscode from 'vscode';
import ignore from 'ignore';
import fs from 'fs';
import { supportedLanguages } from './languageId';
import { join, relative } from 'path';

import { nextEditProvider } from '@/edit/handler';

type PasteEventHandlerFunc = (c: string) => void;

export const onPasteEventHandlers: PasteEventHandlerFunc[] = [];


export function onDidChangeTextDocumentHandler(event: vscode.TextDocumentChangeEvent) {
    // 只处理文件系统中的文档
    if (event.document.uri.scheme !== 'file' || !supportedLanguages.includes(event.document.languageId)) {
        return;
    }
    // Check if the change was due to a paste operation
    if (event.contentChanges.length === 1 && event.contentChanges[0].text.includes('\n')) {
        const pastedText = event.contentChanges[0].text;
        // Handle the pasted text as needed
        for (const handler of onPasteEventHandlers) {
            handler(pastedText);
        }
    }
    if (event.contentChanges.length === 1) {
        nextEditProvider.provideNextEdit(event);
    }
}

export async function findFilesInWorkspace(
    includePattern: string,
    excludePattern?: string,
    maxResults?: number
): Promise<vscode.Uri[]> {
    const workspacePath = getWorkspacePath()?.fsPath || '';
    const files = await vscode.workspace.findFiles(includePattern, excludePattern, maxResults);
    const filePaths = files.map(file => relative(workspacePath, file.fsPath));
    const ig = ignore().add(fs.readFileSync(join(workspacePath, '.gitignore')).toString())
        .add('.git').add('node_modules').add('__pycache__');
    const filterFilePaths = ig.filter(filePaths);
    console.log('debug info: files', files, filePaths, ig, filterFilePaths);
    return filterFilePaths.map(f => vscode.Uri.file(join(workspacePath, f)));
}

export function getWorkspacePath(): vscode.Uri | undefined {
    if (
        vscode.workspace.workspaceFolders &&
        vscode.workspace.workspaceFolders.length > 0
    ) {
        const workspacePath = vscode.workspace.workspaceFolders?.[0].uri;
        return workspacePath;
    }
    return undefined;
}
