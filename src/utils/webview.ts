import { createVsWebviewProtocol, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RpcProtocol } from '@/utils/rpc';
import vscode, { Disposable, Webview } from 'vscode';
import { BehaviorSubject, Subscription } from 'rxjs';
import { agent } from '@/agent';

export abstract class BaseWebviewRpc {

  constructor(
    protected readonly _protocol: RpcProtocol
  ) {
  }

  register(method: string, handler: Rpc<PERSON><PERSON><PERSON>): void {
    this._protocol.register(method, handler);
  }

  protected _req(method: string, ...params: any): Promise<any> {
    return this._protocol.request(method, params);
  }

  async ping(): Promise<string> {
    return this._req('ping');
  }

  async theme(theme: string): Promise<void> {
    return this._req('theme', theme);
  }
}

export interface WebviewContainer {
  readonly webview: Webview;
  readonly visible: boolean;

  show(): Promise<void>;

  focus(): Promise<void>;
}

export function webviewViewToContainer(view: vscode.WebviewView): WebviewContainer {

  return new class implements WebviewContainer {
    get visible() {
      return view.visible;
    };

    get webview() {
      return view.webview;
    }

    async focus(): Promise<void> {
      view.show(true);
    }

    async show(): Promise<void> {
      view.show(false);
    }
  };
}

export function webviewPanelToContainer(panel: vscode.WebviewPanel): WebviewContainer {
  return new class implements WebviewContainer {
    get visible() {
      return panel.visible;
    };

    get webview() {
      return panel.webview;
    }

    async focus(): Promise<void> {
      panel.reveal(undefined, false);
    }

    async show(): Promise<void> {
      panel.reveal(undefined, true);
    }
  };
}

export interface WebviewSetupMetrics {
  readonly startTime: number;
  readonly finishedTime: number;
  readonly setupWebviewCost: number;
}

export abstract class BaseWebviewPanel<A extends BaseWebviewRpc>
  implements Disposable {

  protected readonly _ready$ = new BehaviorSubject<boolean>(false);
  private readonly _disposables: Disposable[] = [];

  private _webviewContainer!: WebviewContainer;
  private _rpc!: A;

  private _setupMetrics?: WebviewSetupMetrics;

  constructor() {
  }

  public get ready() {
    return this._ready$.value;
  }

  protected get rpc(): A {
    return this._rpc;
  }

  protected get webviewView() {
    return this._webviewContainer;
  }

  protected get setupMetrics() {
    return this._setupMetrics;
  }

  protected abstract _resolvePageHtml(): Promise<string>;

  protected abstract _createApi(protocol: RpcProtocol): Promise<A>;

  protected abstract _setup(): Promise<void>;

  protected async _active() {
    this.focus();
  }

  protected async _resetWebview(webview: WebviewContainer) {
    const startTime = Date.now();

    this._webviewContainer = webview;
    await this._setupWebview();
    const setupWebviewCost = Date.now() - startTime;

    await this._initApi();
    await this._setup();

    this._disposables.push(
      vscode.window.onDidChangeActiveColorTheme(() => {
        this.syncTheme();
      })
    );

    const finishedTime = Date.now();
    this._setupMetrics = {
      startTime,
      finishedTime,
      setupWebviewCost,
    };
  }

  private async _setupWebview() {
    const webview = this._webviewContainer.webview;
    webview.options = {
      enableScripts: true
    };
    webview.html = await this._resolvePageHtml();
  }

  private async _initApi() {
    const protocol = createVsWebviewProtocol(this._webviewContainer.webview);
    const api = await this._createApi(protocol);
    this._rpc = api;

    api.register('ping', async () => 'pong');
    api.register('ready', async () => {
      this._ready$.next(true);
      this.syncTheme();
      return true;
    });
    api.register('auth/status', async () => {
      return await agent().client.sendRequest('codebuddy/auth/status');
    });

  }

  async syncTheme() {
    const theme = vscode.window.activeColorTheme.kind === 1
      ? 'light' : 'dark';
    await this.rpc.theme(theme);
  }

  public async show() {
    this._webviewContainer.show();
  }

  public async focus() {
    this._webviewContainer.focus();
  }

  public subscribeReady(next: (value: boolean) => void): Subscription {
    return this._ready$.subscribe(next);
  }

  async readyAndRun<T>(action: () => Promise<T>): Promise<T> {
    if (this.ready) {
      return await action();
    }
    console.log('INFO: Waiting for webview ready...');
    this._active();
    return new Promise<T>((resolve, _reject) => {
      const sub = this._ready$.subscribe(async (ready) => {
        if (ready) {
          sub.unsubscribe();
          resolve(await action());
        }
      });
    });
  }

  dispose() {
    this._ready$.complete();
    this._disposables.forEach(d => d.dispose());
  }
}
