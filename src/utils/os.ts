export const MACOS = 'macos';
export const WIN = 'win';
export const LINUX = 'linux';


export function getOS() {
    switch (process.platform) {
        case 'win32':
            return WIN;
        case 'darwin':
            return MACOS;
        case 'linux':
            return LINUX;
        default:
            return 'unknown';
    }
}

export function getControlKey() {
    switch (getOS()) {
        case MACOS:
            return '⌘';
        case WIN:
            return 'Ctrl';
        case LINUX:
            return 'Ctrl';
    }
    return 'Ctrl';
}
