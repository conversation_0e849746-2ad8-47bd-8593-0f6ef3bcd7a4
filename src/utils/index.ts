import { CancelablePromise } from './CancelablePromise';
import * as semver from 'semver';
import { SemVer } from 'semver';
import { CHANNEL_STABLE } from '@/config';

export { LRU } from './lru';
export { StateSubject } from './StateSubject';
import { execSync } from 'child_process';
import * as vscode from 'vscode';

export function sleep(milliseconds: number) {
  return new Promise(r => setTimeout(r, milliseconds));
}

export const byteToGigabyte = (bytes: number): number =>
  bytes / (1024 * 1024 * 1024);

export const isNil = (v: any) => v === null || v === undefined;

export const isPlainObject = (obj: any) =>
  Object.prototype.toString.call(obj) === '[object Object]';

export const deepClone = <T>(obj: T): T => JSON.parse(JSON.stringify(obj));

// Copy the values of default to target
export const mergeIfAbsent = (target: any, defaults: any): void => {
  for (let p in defaults) {
    if (!Object.prototype.hasOwnProperty.call(defaults, p)) {
      continue;
    }
    if (isNil(target[p]) && !isNil(defaults[p])) {
      target[p] = deepClone(defaults[p]);
    }
  }

  for (let p in target) {
    const defaultValue = defaults[p];
    if (isPlainObject(target[p]) && isPlainObject(defaultValue)) {
      mergeIfAbsent(target[p], defaultValue);
    }
  }
};

export function channelFromVersion(version: string | SemVer | null | undefined): string {
  const parsed = semver.parse(version);
  if (!parsed) {
    return 'unknown';
  }
  return parsed.prerelease[0]?.toString() || CHANNEL_STABLE;
}

export function countLines(text: string): number {
  let count = 0;
  let index = 0;
  const length = text.length;

  while (index < length) {
    if (text[index] === '\n') {
      count++;
    }
    index++;
  }
  if (text[length - 1] !== '\n') {
    count++;
  }
  return count;
}

export function splitLines(input: string) {
  return input.match(/.*(?:$|\r?\n)/g)?.filter(Boolean) || []; // Split lines and keep newline character
}

export function splitWords(input: string) {
  return input.match(/\w+|\W+/g)?.filter(Boolean) || []; // Split consecutive words and non-words
}

export function isBlank(input: string) {
  return /^\s*$/.test(input);
}

export function countBlankPrefix(input: string): number {
  let count = 0;
  for (let i = 0; i < input.length; i++) {
    const c = input[i];
    if (c === ' ' || c === '\t') {
      count++;
    } else {
      break;
    }
  }
  return count;
}

export function cancelable<T>(
  promise: Promise<T>,
  cancel: () => void
): CancelablePromise<T> {
  return new CancelablePromise((resolve, reject, onCancel) => {
    promise
      .then((resp: T) => {
        resolve(resp);
      })
      .catch((err: Error) => {
        reject(err);
      });
    onCancel(() => {
      cancel();
    });
  });
}

// deprecated
export function getCollectionName(): string | undefined {
  const origin = getOrigin();
  if (!origin) {
    return;
  }
  return origin.split('@')?.[1]?.replace(/[-\/\.:]/g, '_');
}

export function getOrigin(): string | undefined {
  const curDir = vscode.workspace?.workspaceFolders;
  if (!curDir || curDir.length === 0) {
    console.log('No current directory');
    return;
  }
  const pwd = curDir[0].uri.fsPath;
  const cmd = 'git config --get remote.origin.url';
  try {
    return execSync(cmd, { cwd: pwd }).toString().trim();
  } catch (error: any) {
    console.log(`Failed to get remote origin url: ${error.message}`);
    return;
  }
}
