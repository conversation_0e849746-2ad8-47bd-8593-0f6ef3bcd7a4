import { describe, it, expect, beforeEach } from 'vitest';
import { LRU } from '../lru';

describe('LRU', () => {
  let lru: LRU<string, number>;

  beforeEach(() => {
    // Create a new LRU instance with size 3 before each test
    lru = new LRU<string, number>(3);
  });

  it('should add and retrieve items', () => {
    lru.add('a', 1);
    lru.add('b', 2);

    expect(lru.has('a')).toBe(true);
    expect(lru.has('b')).toBe(true);
    expect(lru.has('c')).toBe(false);

    expect(lru.get('a')).toBe(1);
    expect(lru.get('b')).toBe(2);
    expect(lru.get('c')).toBe(undefined);
  });

  it('should maintain LRU order and remove oldest items when full', () => {
    // Add items to fill the cache
    lru.add('a', 1);
    lru.add('b', 2);
    lru.add('c', 3);

    // All items should be present
    expect(lru.has('a')).toBe(true);
    expect(lru.has('b')).toBe(true);
    expect(lru.has('c')).toBe(true);

    // Adding a new item should remove the oldest item (a)
    lru.add('d', 4);

    expect(lru.has('a')).toBe(false);
    expect(lru.has('b')).toBe(true);
    expect(lru.has('c')).toBe(true);
    expect(lru.has('d')).toBe(true);
  });

  it('should move an item to the end of LRU order when re-added', () => {
    // Fill the cache
    lru.add('a', 1);
    lru.add('b', 2);
    lru.add('c', 3);

    // Re-add an existing item which should move it to the end
    lru.add('a', 11);

    // Add a new item which should evict the oldest (now 'b')
    lru.add('d', 4);

    // 'b' should be removed as the oldest item
    expect(lru.has('a')).toBe(true);
    expect(lru.has('b')).toBe(false);
    expect(lru.has('c')).toBe(true);
    expect(lru.has('d')).toBe(true);

    // The updated value should be present
    expect(lru.get('a')).toBe(11);
  });

  it('should handle empty cache', () => {
    expect(lru.has('anything')).toBe(false);
    expect(lru.get('anything')).toBe(undefined);
  });

  it('should create a cache of size 0', () => {
    const emptyLRU = new LRU<string, number>(0);

    // Adding any item should result in immediate eviction
    emptyLRU.add('a', 1);
    expect(emptyLRU.has('a')).toBe(false);
  });
});
