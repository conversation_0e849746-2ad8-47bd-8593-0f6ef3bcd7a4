include:
  - project: ep/devops/ci-lib
    ref: main
    file: lib.all.yml
  - project: 'ep-public/portal/cicd-tools'
    ref: v1
    file:
      - 'gitlabci/precommit/precommit.yaml'
  - .gitlab-ci/lib/*.yml
  - .gitlab-ci/rules/*.yml

pre-commit:
  extends: .ep-ci-pre-commit
  stage: build

stages:
  - build
  - unit-test
  - upload

variables:
  APP_CI_DEBUG: 'true'
  APP_NAME: codebuddy-vscode
  APP_DISPLAY_NAME: 'CodeBuddy VSCode Plugin'
  APP_SHARED_ENV: .gitlab-ci.shared.env
  # *********************: https://registry.npmmirror.com

workflow:
  rules:
    - if: >-
        $CI_PIPELINE_SOURCE == "push"
        && $CI_COMMIT_MESSAGE =~ /(^|\s+)SKIP_CI($|\s+)/
      when: never
    - when: always
