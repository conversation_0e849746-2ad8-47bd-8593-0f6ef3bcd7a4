#!/usr/bin/env bash
set -e

APP_VERSION=$(jq -r .version package.json)
APP_NAME="codebuddy-vscode"
FILE="${APP_NAME}-${APP_VERSION}.vsix"
mkdir -p ./out
pnpm run package:prod -o "./out/$FILE"

upload() {
  local file=$1
  name="$(basename -- "$file")"
  url="https://gitlabee.chehejia.com/api/v4/projects/11709/packages/generic/$APP_NAME/$APP_VERSION/$name"
  echo "$url"
  echo "--> Uploading $name"
  curl --header "$TOKEN_HEADER" \
    --upload-file "$file" \
    "$url"
  echo "--> Done"
}

for file in ./out/*.vsix; do
  upload "$file";
done;
