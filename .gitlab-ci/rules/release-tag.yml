.with-rules: &with-rules
  rules:
    - if: >-
        $CI_COMMIT_TAG =~ /^v[0-9]+/

.app-pre-release: &app-pre |
  set -e
  eval "$(fnm env --shell bash)"
  export APP_VERSION="${CI_COMMIT_TAG:1}"
  echo "APP_VERSION: $APP_VERSION"
  jq --arg v "$APP_VERSION" '.version = $v' package.json > package.json.tmp
  mv package.json.tmp package.json

plugin-build:
  <<: *with-rules
  extends: .pnpm-base
  stage: build
  script:
    - !reference [ .app-pre-release ]
    - pnpm install --no-frozen-lockfile
    - |
      # Fetch dependencies
      pnpm run compile:prod
      mkdir -p ./out
      CI_COMMIT_TAG=${CI_COMMIT_TAG} pnpm run package:prod -o ./out/"${APP_NAME}-${APP_VERSION}.vsix"
  artifacts:
    name: distributions
    paths:
      - ./out

plugin-upload:
  <<: *with-rules
  stage: upload
  extends: .pnpm-base
  cache: { }
  script:
    - !reference [ .app-pre-release ]
    - |
      echo "APP_NAME $APP_NAME"
      APP_REGISTRY="https://gitlabee.chehejia.com/api/v4/projects/11709/packages/generic"
      [ -n "$APP_RELEASE_UPLOAD_AUTH_TOKEN" ] \
        && TOKEN_HEADER="${APP_RELEASE_UPLOAD_AUTH_HEADER}: ${APP_RELEASE_UPLOAD_AUTH_TOKEN}"
      upload() {
        local file=$1
        name="$(basename -- "$file")"
        url="$APP_REGISTRY/$APP_NAME/$APP_VERSION/$name"
        echo "$url"
        echo "--> Uploading $name"
        curl --header "$TOKEN_HEADER" \
          --upload-file "$file" \
          "$url"
        echo ""
        echo "Uploaded $name"
      }
      for file in ./out/*.vsix; do
        upload $file;
      done;
