{"makainan": [{"component": "codebuddy-vscode", "currentVersion": "2.0.0-alpha.2", "environment": "normal", "errMsg": "Internal: 请在重新安装CodeBuddy之前重新启动 VS Code。", "status": "failure", "targetVersion": "2.0.0-alpha.6", "timestamp": "2025-07-08T10:25:12.881Z"}, {"component": "codebuddy-vscode", "currentVersion": "2.0.0-alpha.2", "environment": "normal", "errMsg": "Rename: ENOTEMPTY: directory not empty, rename '/Users/<USER>/.vscode/extensions/.0f71490d-abef-4c28-8caa-769d6823bc20' -> '/Users/<USER>/.vscode/extensions/liauto.vscode-li-codebuddy-2.0.0-alpha.6'", "status": "failure", "targetVersion": "2.0.0-alpha.6", "timestamp": "2025-07-08T09:19:43.041Z"}, {"component": "codebuddy-vscode", "currentVersion": "2.0.0-alpha.2", "environment": "normal", "errMsg": "Extract: End of central directory record signature not found. Either not a zip file, or file is truncated.", "status": "failure", "targetVersion": "2.0.0-alpha.4", "timestamp": "2025-07-04T02:17:40.056Z"}, {"component": "codebuddy-vscode", "currentVersion": "2.0.0-alpha.2", "environment": "normal", "errMsg": "Extract: 在 Zip 中找不到 extension/package.json。", "status": "failure", "targetVersion": "2.0.0-alpha.4", "timestamp": "2025-07-03T21:23:12.050Z"}]}