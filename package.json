{"name": "vscode-li-codebuddy", "publisher": "LiAuto", "license": "Private", "displayName": "CodeBuddy", "description": "CodeBuddy 是企业智能软件工效团队自研的基于⼤语⾔模型（LLM）的智能编程助手（Coding Copilot），希望可以通过这款工具提升开发者效率，让代码编写更简单。", "version": "1.2.0-dev", "codebuddy": {"channel": "--deprecated--"}, "keywords": ["codebuddy", "copilot", "code", "coding", "llm", "AI"], "icon": "assets/images/logo.png", "engines": {"vscode": "^1.80.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "packageManager": "pnpm@10.6.2", "main": "./dist/node/extension.js", "repository": "https://gitlabee.chehejia.com/ep/integration/codebuddy-vscode", "scripts": {"lint": "eslint src --ext ts", "vitest": "npx vitest run --coverage --reporter=junit --outputFile=coverage/junit.xml", "watch": "webpack --watch", "dev": "pnpm compile && code --extensionDevelopmentPath=$PWD --disable-extensions", "compile": "pnpm run lint && webpack", "compile:prod": "pnpm run lint && webpack --mode production --devtool hidden-source-map", "package:prod": "pnpm run lint && pnpm compile:prod && pnpm vsce package --no-dependencies", "upload": "sh -c ./scripts/upload.sh"}, "devDependencies": {"@types/glob": "^7.2.0", "@types/mocha": "^10.0.1", "@types/node": "16.x", "@types/qrcode": "^1.5.5", "@types/semver": "^7.5.4", "@types/vscode": "~1.80.0", "@typescript-eslint/eslint-plugin": "^5.31.0", "@typescript-eslint/parser": "^5.31.0", "@vitest/coverage-v8": "3.1.3", "@vscode/test-electron": "^2.1.5", "@vscode/test-web": "^0.0.44", "@vscode/vsce": "^2.21.1", "assert": "^2.0.0", "eslint": "^8.20.0", "glob": "^8.0.3", "mocha": "^10.0.0", "node-loader": "^2.0.0", "process": "^0.11.10", "ts-loader": "^9.3.1", "typescript": "^4.7.4", "vitest": "^3.1.3", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "dependencies": {"@sentry/node": "^9.36.0", "@sentry/tracing": "^7.120.3", "@types/deep-equal": "^1.0.1", "@types/diff": "^5.0.5", "@types/diff-match-patch": "^1.0.36", "@types/fs-extra": "^11.0.2", "@types/object-hash": "^3.0.4", "@types/uuid": "^9.0.4", "@xstate/fsm": "^2.0.1", "axios": "^1.5.0", "axios-observable": "^2.0.0", "compare-versions": "^6.1.0", "compressing": "^1.10.0", "dayjs": "^1.11.10", "deep-equal": "^2.2.2", "diff": "^5.2.0", "diff-match-patch": "^1.0.5", "fastest-levenshtein": "^1.0.16", "form-data": "^4.0.0", "fs-extra": "^11.1.1", "ignore": "^6.0.2", "jwt-decode": "^4.0.0", "lru-cache": "^10.0.1", "object-hash": "^3.0.0", "object-sizeof": "^2.6.3", "pino": "^8.15.1", "qrcode": "^1.5.4", "rotating-file-stream": "^3.1.1", "rxjs": "^7.8.1", "semver": "^7.5.4", "uuid": "^9.0.1", "vscode-languageclient": "^8.1.0", "vscode-position-tracking": "^0.1.0", "yaml": "^2.4.5", "zustand": "^5.0.3"}, "contributes": {"viewsContainers": {"activitybar": []}, "views": {}, "commands": [{"command": "li.codebuddy.chat.explain", "title": "解释代码"}, {"command": "li.codebuddy.chat.fix", "title": "修复代码"}, {"command": "li.codebuddy.chat.refactor", "title": "代码重构"}, {"command": "li.codebuddy.action.copilot.toggleEnabled", "title": "CodeBuddy: Toggle Code Completion On/Off"}, {"command": "li.codebuddy.action.openDocumentation", "title": "CodeBuddy: Open Document"}, {"command": "li.codebuddy.action.openLogs", "title": "CodeBuddy: Open Logging"}, {"command": "li.codebuddy.action.checkUpgrade", "title": "CodeBuddy: Check Upgrade"}, {"command": "li.codebuddy.action.openSettings", "title": "CodeBuddy: Open Settings"}, {"command": "li.codebuddy.action.agent.forceReinstall", "title": "CodeBuddy: Force Reinstall Kernel"}, {"command": "li.codebuddy.action.agent.checkUpgrade", "title": "CodeBuddy: Check Upgrade for Kernel"}, {"command": "li.codebuddy.nextEdit.accept", "title": "Next Edit 接受"}, {"command": "li.codebuddy.nextEdit.reject", "title": "Next Edit 拒绝"}, {"command": "li.codebuddy.nextEdit.cancel", "title": "Next Edit 取消"}, {"command": "li.codebuddy.nextEdit.background.next", "title": "Next Edit 跳转下一个"}, {"command": "li.codebuddy.nextEdit.background.previous", "title": "Next Edit 跳转上一个"}, {"command": "li.codebuddy.auth.login", "title": "CodeBuddy: <PERSON><PERSON>"}, {"command": "li.codebuddy.auth.login.callback", "title": "CodeBuddy: <PERSON><PERSON>"}, {"command": "li.codebuddy.action.noop", "title": "CodeBuddy"}, {"command": "li.codebuddy.action.agent.restart", "title": "CodeBuddy: <PERSON><PERSON>"}, {"command": "li.codebuddy.chat.inline-chat", "title": "CodeBuddy: 启动 Inline Chat"}], "keybindings": [{"key": "tab", "command": "li.codebuddy.nextEdit.accept", "when": "li.codebuddy.copilot.nextEdit.enabled && li.codebuddy.copilot.nextEdit.acceptable"}, {"key": "cmd+backspace", "command": "li.codebuddy.nextEdit.reject", "when": "li.codebuddy.copilot.nextEdit.enabled && li.codebuddy.copilot.nextEdit.acceptable", "mac": "cmd+backspace", "win": "ctrl+backspace", "linux": "ctrl+backspace"}, {"key": "escape", "command": "li.codebuddy.nextEdit.cancel", "when": "li.codebuddy.copilot.nextEdit.enabled && li.codebuddy.copilot.nextEdit.acceptable"}, {"key": "cmd+;", "command": "li.codebuddy.nextEdit.background.next", "when": "li.codebuddy.copilot.nextEdit.enabled", "mac": "cmd+;", "win": "ctrl+;", "linux": "ctrl+;"}, {"key": "cmd+shift+;", "command": "li.codebuddy.nextEdit.background.previous", "when": "li.codebuddy.copilot.nextEdit.enabled", "mac": "cmd+shift+;", "win": "ctrl+shift+;", "linux": "ctrl+shift+;"}, {"key": "cmd+i", "command": "li.codebuddy.chat.inline-chat", "when": "li.codebuddy.copilot.inlineChat.enabled", "mac": "cmd+i", "win": "ctrl+i", "linux": "ctrl+i"}], "submenus": [{"id": "li-codebuddy-chat.submenus", "label": "CodeBuddy"}], "menus": {"editor/context": [{"submenu": "li-codebuddy-chat.submenus", "group": "codebuddy"}], "li-codebuddy-chat.submenus": [{"command": "li.codebuddy.chat.explain", "group": "codebuddy-60-chat@60"}, {"command": "li.codebuddy.chat.fix", "group": "codebuddy-60-chat@61"}, {"command": "li.codebuddy.chat.refactor", "group": "codebuddy-60-chat@62"}], "commandPalette": []}, "configuration": {"title": "CodeBuddy", "properties": {"li.codebuddy.copilot.enabled": {"type": "boolean", "default": true, "description": "是否开启补全"}, "li.codebuddy.copilot.inlineChat.enabled": {"type": "boolean", "default": false, "description": "是否启用 Inline Chat"}, "li.codebuddy.advance": {"type": "object", "description": "插件进阶设置", "properties": {"consoleDebugEnabled": {"type": "boolean", "default": false, "description": "是否启用 Console 调试"}, "logs": {"type": "object", "properties": {"level": {"type": "string", "default": "warn", "markdownDescription": "日志输出等级", "enum": ["trace", "debug", "info", "warn", "error", "off"]}}}}}, "li.codebuddy.copilot.advance": {"type": "object", "description": "Copilot 进阶设置", "properties": {"suggestionDelay": {"type": "number", "default": 150, "minimum": 0, "description": "请求发送到服务器的延迟 (单位: 毫秒)"}}}, "li.codebuddy.copilot.nextEdit.enabled": {"type": "boolean", "default": false, "description": "是否启用 Next Edit"}}}}}