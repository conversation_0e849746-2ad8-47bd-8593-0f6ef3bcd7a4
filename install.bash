code --install-extension codebuddy-vscode-1.6.1-dev.15.vsix --force
code --install-extension codefactory-v0.1.2-alpha.19.vsix --force 

rm -rf /Users/<USER>/.vscode/extensions/liauto.code-factory-0.5.0-alpha.4/ 
rm -rf /Users/<USER>/.vscode/extensions/liauto.vscode-li-codebuddy-1.6.1-dev.23/

# code --install-extension codebuddy-vscode-1.6.1-dev.23.vsix
# code --install-extension codefactory-v0.5.0-alpha.4.vsix


# Installing extensions...
# Extension 'codebuddy-vscode-1.6.1-dev.15.vsix' was successfully installed.
# Installing extensions...
# Error: Please restart VS Code before reinstalling CodeFactory.
#     at al.h (file:///Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/code/node/cliProcessMain.js:56:48860) {
#   code: 'Internal',
#   name: 'Internal'
# }
# Failed Installing Extensions: file:///Users/<USER>/wsp/coding-copilot/codebuddy-vscode/codefactory-v0.1.2-alpha.19.vsix
# Installing extensions...
# Extension 'codebuddy-vscode-1.6.1-dev.23.vsix' was successfully installed.
# Installing extensions...
# Error: Please restart VS Code before reinstalling CodeFactory.
#     at al.h (file:///Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/code/node/cliProcessMain.js:56:48697) {
#   code: 'Internal',
#   name: 'Internal'
# }
# Failed Installing Extensions: file:///Users/<USER>/wsp/coding-copilot/codebuddy-vscode/codefactory-v0.5.0-alpha.4.vsix


# Installing extensions...
# Extension 'codebuddy-vscode-1.6.1-dev.15.vsix' was successfully installed.
# Installing extensions...
# Extension 'codefactory-v0.1.2-alpha.19.vsix' was successfully installed.
# Installing extensions...
# Error: Please restart VS Code before reinstalling CodeBuddy.
#     at al.h (file:///Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/code/node/cliProcessMain.js:56:48697) {
#   code: 'Internal',
#   name: 'Internal'
# }
# Failed Installing Extensions: file:///Users/<USER>/wsp/coding-copilot/codebuddy-vscode/codebuddy-vscode-1.6.1-dev.23.vsix
# Installing extensions...
# Extension 'codefactory-v0.5.0-alpha.4.vsix' was successfully installed.
